import { KeyComboTag } from '@blueprintjs/core';
import * as React from 'react';
import { FunctionComponent } from 'react';

const Instructions: FunctionComponent<Record<string, never>> = () => (
  <div className="row bz-grid-hotkeys" style={{ float: 'left' }}>
    <div>
      <KeyComboTag combo="Shift + Alt + N" />
      Create a tab
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + Number" />
      or
      <KeyComboTag combo="Shift + Alt + Number" />
      Switch Tab
    </div>
    <div>
      <KeyComboTag combo="Shift + Alt + X" />
      Remove Tab
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + /" />
      or
      <KeyComboTag combo="Shift + Alt + /" />
      Go to Title
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + B" />
      or
      <KeyComboTag combo="Shift + Alt + B" />
      Go to Body
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + K" />
      or
      <KeyComboTag combo="Shift + Alt + K" />
      Go to Tickers
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + G" />
      or
      <KeyComboTag combo="Shift + Alt + G" />
      Go to Tags
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + C" />
      or
      <KeyComboTag combo="Shift + Alt + C" />
      Go to Categories
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + S" />
      <KeyComboTag combo="Shift + Alt + S" />
      Go to Sentiment
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + Z" />
      or
      <KeyComboTag combo="Shift + Alt + Z" />
      Toggle BZ
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + P" />
      or
      <KeyComboTag combo="Shift + Alt + P" />
      Toggle PRO
    </div>
    <div>
      <KeyComboTag combo="Shift + Ctrl + ENTER" />
      or
      <KeyComboTag combo="Shift + Alt + ENTER" />
      Post the Article
    </div>
  </div>
);

export default Instructions;
