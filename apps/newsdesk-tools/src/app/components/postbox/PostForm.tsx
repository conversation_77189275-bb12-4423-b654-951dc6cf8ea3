import { Button, Checkbox, Classes, Intent } from '@blueprintjs/core';
import classNames from 'classnames';
import { equals, isNil, range, reverse } from 'ramda';
import * as React from 'react';
import Select from 'react-select';
import { MultiValueProps } from 'react-select';
import TextareaAutosize from 'react-textarea-autosize';

import AsyncSelect from 'react-select/async';
import Async from 'react-select';
import { autoCompleteOptions, tagsOptions, toOptions } from '../../services/autocomplete';
import { Events } from '../../utils/eventEntities';
import Callout from '../Callout';
import Editor from './Editor';
import { HotKeys } from './entities';
import Instructions from './Instructions';

import { addPost, setPosterField, setPosterFields } from '../../actions/news';
import { Category, News, Post, Tag, Ticker } from '../../reducers/entities/newsEntities';
import { generateSelectInputValue } from '../../utils/newsUtils';
import { countNumberOfWordsAndLetters } from '../utils';
import { connect } from 'react-redux';
import { selectSession } from '../../selectors/sessionSelectors';
import { RootState } from '../../state';
import { UserData } from '../../reducers/entities/sessionEntities';
import { fetchPostboxStories, fetchVerticals } from '../../services/postbox';
import { fetchNewsAsync } from '../../actions/postbox';
import { FetchNews } from '../../reducers/entities/postboxEntities';
import { withScreenSizeHooksHOC } from './withScreenSizeHooksHOC';
import { Collapse } from 'antd';
import { IconNames } from '@blueprintjs/icons';
import { isEmpty } from 'lodash';
import { Vertical } from '@benzinga/verticals-manager';

interface DispatchableActions {
  addPost: typeof addPost;
  setPosterField: typeof setPosterField;
  setPosterFields: typeof setPosterFields;
  fetchNewsAsync: typeof fetchNewsAsync;
}

interface OwnProps {
  changeTab: (key: string) => void;
  errorMessage?: string;
  addRemoveTab: (action: string) => void;
  post: Post;
  story: News;
  postBoxStory: FetchNews;
  submitPost(post: Post): void;
}
interface WindowSize {
  width: number;
  height: number;
}
interface ReduxState {
  loggedIn?: boolean;
  loggingIn: boolean;
  serverToken: string;
  user: UserData | null;
  windowSize: WindowSize;
}

interface Sentiment {
  data: string;
  label: string;
  value: string;
}

interface Author {
  data: string;
  label: string;
  value: boolean;
}

export interface BreakpointValues {
  LARGE: number;
  SMALL: number;
  XLARGE: number;
  XSMALL: number;
}

const WINDOW_WIDTH_BREAKPOINTS: BreakpointValues = {
  LARGE: 992,
  SMALL: 700,
  XLARGE: 1200,
  XSMALL: 0,
};

interface State {
  inputValue: string;
  verticals: Vertical[];
  selectedVerticals: Vertical[];
}

type Props = OwnProps & DispatchableActions & ReduxState;

const sentimentOptions = toOptions(reverse(range(-3, 4)));

class PostForm extends React.Component<Props, State> {
  bodyTextEditor?: HTMLElement;
  titleTextBox: HTMLElement;
  categoriesComboBox: React.RefObject<Select<Category>> = React.createRef();
  primaryTickerComboBox: React.RefObject<Async<Ticker>> = React.createRef();
  secondaryTickerComboBox: React.RefObject<Async<Ticker>> = React.createRef();
  sentimentComboBox: React.RefObject<Select<Sentiment>> = React.createRef();
  authorComboBox: React.RefObject<Select<Author>> = React.createRef();
  tagsComboBox: React.RefObject<Async<Tag>> = React.createRef();

  constructor(props: Props) {
    super(props);
    this.state = {
      inputValue: '',
      selectedVerticals: [],
      verticals: [],
    };
  }

  componentDidMount() {
    const queryParams = new URLSearchParams(window.location.search);
    const type = queryParams.get('type');
    const id = queryParams.get('id');
    if (id && type) {
      this.fetchPostboxStories(id, type);
    }
    this.titleTextBox.focus();
    this.getVerticalsOptions();
    document.addEventListener(Events.keydown, this.hotkeyHandler);
  }
  componentWillUnmount() {
    document.removeEventListener(Events.keydown, this.hotkeyHandler);
  }
  getVerticalsOptions = () => {
    return fetchVerticals()
      .then(res => {
        const result = res.data
          .filter(vertical => vertical.verticalSymbols.length !== 0)
          .map(vertical => ({
            data: vertical.verticalSymbols,
            label: vertical.verticalName,
            value: vertical.verticalSymbols,
          }));
        this.setState({ verticals: result });
        return result;
      })
      .catch(errorObj => {
        console.error('Error fetching verticals:', errorObj);
      });
  };
  fetchPostboxStories = (id: string, type: string) => {
    fetchPostboxStories(type, id, this.props.serverToken)
      .then(res => {
        const { data } = res;
        //this.setState({ postBoxData: data });
        this.props.fetchNewsAsync(data);
      })
      .catch(errorObj => {
        console.log('error is', errorObj); // need to add error state file fetching data
        const {
          data: { errors },
        } = errorObj.response;
        console.log(errors);
      });
  };

  getOptionLabel = (option: Ticker): string => {
    const { data } = option;
    return `${data.symbol} (${data.exchange}, ${data.country}) | ${data.name}`;
  };

  getOptionValue = (option: Ticker): string => {
    const { data } = option;
    return `${data.exchange}-${data.symbol}`;
  };

  handleChangeBody = (body: string) => {
    const { charCount, wordCount } = countNumberOfWordsAndLetters(body);
    this.props.setPosterFields({ body, charCount, wordCount });
  };

  submitPost = () => {
    const { post, submitPost } = this.props;
    submitPost(post);
    this.props.fetchNewsAsync({
      Body: '',
      Channels: [],
      Tags: [],
      Tickers: [],
      Title: '',
    });
  };

  hotkeyHandler = (event: KeyboardEvent) => {
    const key = event.code;
    if (event.shiftKey && (event.ctrlKey || event.altKey)) {
      const { post, setPosterField } = this.props;
      const { title } = post;
      switch (key) {
        case HotKeys.Key_E: {
          let { editorMode } = post;
          editorMode = equals(editorMode, 'html') ? 'plain' : 'html';
          setPosterField('editorMode', editorMode);
          break;
        }
        case HotKeys.Key_B:
          if (this.bodyTextEditor) {
            this.bodyTextEditor.focus();
          }
          break;
        case HotKeys.Key_N:
          this.props.addRemoveTab('add');
          break;
        case HotKeys.Key_FORWARD_SLASH:
          this.titleTextBox.focus();
          break;
        case HotKeys.Key_P:
          setPosterField('isBzProPost', !post.isBzProPost);
          break;
        case HotKeys.Key_Z:
          setPosterField('isBzPost', !post.isBzPost);
          break;
        case HotKeys.Key_K:
          this.primaryTickerComboBox.current.focus();
          break;
        case HotKeys.Key_C:
          this.categoriesComboBox.current.focus();
          break;
        case HotKeys.Key_G:
          this.tagsComboBox.current.focus();
          break;
        case HotKeys.Key_S:
          this.sentimentComboBox.current.focus();
          break;
        case HotKeys.Key_ENTER:
          if (!isNil(title) && title.length >= 10) {
            this.submitPost();
          }
          break;
        case 'Digit1':
        case 'Digit2':
        case 'Digit3':
        case 'Digit4':
        case 'Digit5':
        case 'Digit6':
        case 'Digit7':
        case 'Digit8':
        case 'Digit9': {
          this.props.changeTab(event.code);
          break;
        }
        case 'KeyX': {
          this.props.addRemoveTab('remove');
          break;
        }
        default:
          return;
      }
      event.preventDefault();
      event.stopPropagation();
    }
  };

  textBox = ref => {
    this.titleTextBox = ref;
  };

  handleTitleChange = event => {
    this.props.setPosterField('title', event.target.value);
  };
  handlePrimaryTickerChange = tickers => {
    this.props.setPosterField('primaryTickers', tickers);
  };
  handleSecondaryTickerChange = tickers => {
    this.props.setPosterField('secondaryTickers', tickers);
  };
  handleCategoryChange = categories => {
    this.props.setPosterField('categories', categories);
  };
  handleTagChange = tags => {
    this.props.setPosterField('tags', tags);
  };
  onInputChange = (newValue: string) => {
    if (!isEmpty(newValue)) this.setState({ inputValue: newValue });
  };
  addTag = event => {
    event.preventDefault();
    const tags: any = [];
    const val = this.state.inputValue;
    console.log(tags);
    tags.push({ data: val, label: val, value: val });
    this.props.setPosterField('tags', tags);
  };
  handleSentimentChange = ({ value }: Sentiment) => {
    this.props.setPosterField('sentiment', value);
  };
  handleBzProPostChange = event => {
    this.props.setPosterField('isBzProPost', event.currentTarget.checked);
  };
  handleBzPostChange = event => {
    this.props.setPosterField('isBzPost', event.currentTarget.checked);
  };
  handleDndChange = event => {
    this.props.setPosterField('dnd', event.currentTarget.checked);
  };
  handleAuthorChange = author => {
    this.props.setPosterField('post_as_newsdesk_user', author.value);
  };
  setEditorRef = ref => {
    this.bodyTextEditor = ref;
  };

  handleVerticalChange = (event: any) => {
    this.setState({ selectedVerticals: event });
    const set = new Set(event.flatMap(e => e.data));
    const primaryTickerFormat = Array.from(set).map(symbol => ({
      data: {
        exchange: '',
        name: symbol,
        symbol,
      },
      label: symbol,
      value: symbol,
    }));
    this.handlePrimaryTickerChange(primaryTickerFormat);
  };
  fillTitle = () => {
    if (this.props.postBoxStory && Object.values(this.props.postBoxStory).length > 1 && this.props.postBoxStory.Title) {
      this.props.setPosterField('title', this.props.postBoxStory.Title);
      return this.props.postBoxStory.Title;
    } else {
      return this.props.post.title;
    }
  };

  fillTickers = () => {
    if (this.props.postBoxStory && Object.values(this.props.postBoxStory).length > 1) {
      const primaryTickers: Ticker[] = [];
      const secondaryTickers: Ticker[] = [];
      if (this.props.postBoxStory.Tickers && this.props.postBoxStory.Tickers.length > 0) {
        this.props.postBoxStory.Tickers.forEach(e => {
          if (e.primary) {
            primaryTickers.push({
              data: { exchange: '', name: e.name, symbol: e.name },
              label: '',
              value: '',
            });
          } else {
            secondaryTickers.push({
              data: { exchange: '', name: e.name, symbol: e.name },
              label: '',
              value: '',
            });
          }
        });
        this.handlePrimaryTickerChange(primaryTickers);
        this.handleSecondaryTickerChange(secondaryTickers);
        return { primary: primaryTickers, secondary: secondaryTickers };
      } else {
        return { primary: [], secondary: [] };
      }
    }
    return { primary: [], secondary: [] };
  };

  fillCategories = () => {
    if (this.props.postBoxStory && Object.values(this.props.postBoxStory).length > 1) {
      const categories: Category[] = [];
      if (this.props.postBoxStory.Channels && this.props.postBoxStory.Channels.length > 0) {
        this.props.postBoxStory.Channels.forEach(e => {
          categories.push({
            data: { channel: e.name, id: e.tid, parent: 0, weight: 0 },
            label: e.name,
            value: e.name,
          });
        });
        this.handleCategoryChange(categories);
        return categories;
      }
    }
    return [];
  };

  fillTags = () => {
    if (this.props.postBoxStory && Object.values(this.props.postBoxStory).length > 1) {
      const tags: any[] = [];
      if (this.props.postBoxStory.Tags && this.props.postBoxStory.Tags.length > 0) {
        this.props.postBoxStory.Tags.forEach(e => {
          tags.push({
            data: { id: e.tid, parent: 0, tag: e.name, weight: 0 },
            label: e.name,
            value: e.name,
          });
        });
        this.handleTagChange(tags);
      }
      return tags;
    }
  };

  isWindowSizeIsSmall = () => {
    return this.props.windowSize.width <= WINDOW_WIDTH_BREAKPOINTS.SMALL;
  };

  render() {
    const { post, story, user } = this.props;
    if (!post) return;
    const {
      body,
      categories,
      charCount,
      dnd,
      editorMode,
      isBzPost,
      isBzProPost,
      post_as_newsdesk_user,
      primaryTickers,
      secondaryTickers,
      sentiment,
      tags,
      title,
      wordCount,
    } = post;
    const authorOptions = [
      { data: 'Newsdesk', label: 'Newsdesk', value: true },
      { data: user?.uuid, label: user?.display_name, value: false },
    ];

    const multiValue = (props: MultiValueProps<Ticker>) => {
      const {
        data: {
          data: { exchange, symbol },
        },
      } = props;
      const content = generateSelectInputValue(exchange, symbol);
      return <span>{content}</span>;
    };

    const { allCategories, categoriesLoading, errorMessage } = story;

    const selectSentiment = {
      data: sentiment,
      label: sentiment,
      value: sentiment,
    };

    const tickerRender = () => {
      return (
        <AsyncSelect
          cacheOptions={false}
          className="row"
          components={{ MultiValueLabel: multiValue }}
          defaultOptions={false}
          getOptionLabel={this.getOptionLabel}
          getOptionValue={this.getOptionValue}
          isMulti
          loadOptions={autoCompleteOptions('tickers', 'symbol')}
          onChange={this.handlePrimaryTickerChange}
          placeholder="Primary Tickers..."
          ref={this.primaryTickerComboBox as any}
          value={primaryTickers ? primaryTickers : this.fillTickers()?.primary}
        />
      );
    };

    const metaRender = () => {
      return (
        <>
          <AsyncSelect
            cacheOptions={false}
            className="row"
            components={{ MultiValueLabel: multiValue }}
            defaultOptions={false}
            getOptionLabel={this.getOptionLabel}
            getOptionValue={this.getOptionValue}
            isMulti
            loadOptions={autoCompleteOptions('tickers', 'symbol')}
            onChange={this.handleSecondaryTickerChange}
            placeholder="Secondary Tickers..."
            ref={this.secondaryTickerComboBox as any}
            value={secondaryTickers ? secondaryTickers : this.fillTickers()?.secondary}
          />
          <Select
            className="row"
            isMulti
            onChange={this.handleVerticalChange}
            options={this.state.verticals}
            placeholder="Topic Tickers..."
            value={this.state.selectedVerticals}
          />
          <Select
            className="row"
            isLoading={categoriesLoading}
            isMulti
            onChange={this.handleCategoryChange}
            options={allCategories}
            placeholder="Categories..."
            ref={this.categoriesComboBox}
            value={categories ? categories : this.fillCategories()}
          />
          <div style={{ alignItems: 'center', display: 'flex' }}>
            <AsyncSelect
              cacheOptions={false}
              className="row tag-select"
              defaultOptions={false}
              isMulti
              loadOptions={tagsOptions}
              onChange={this.handleTagChange}
              onInputChange={this.onInputChange}
              placeholder="Tags..."
              ref={this.tagsComboBox as any}
              value={tags ? tags : this.fillTags()}
            />
            <div style={{ marginBottom: '10px', marginLeft: '5px' }}>
              <Button icon={IconNames.ADD} intent={Intent.NONE} onClick={this.addTag} />
            </div>
          </div>
          <Select
            className="row"
            isClearable={false}
            onChange={this.handleSentimentChange}
            options={sentimentOptions}
            placeholder="Sentiment..."
            ref={this.sentimentComboBox}
            value={selectSentiment || null}
          />
        </>
      );
    };

    const { Panel } = Collapse;
    return (
      <div className="bz-postbox">
        <div className="bz-form">
          <div className="bz-form-left">
            <label className={classNames(Classes.LABEL, Classes.TEXT_MUTED)}>
              Min (10) Max (512)
              <TextareaAutosize
                className="bp5-input bp5-fill row no-resize"
                dir="auto"
                maxLength={512}
                minLength={10}
                onChange={this.handleTitleChange}
                placeholder="Title"
                ref={this.textBox}
                value={this.fillTitle()}
              />
            </label>
            {this.isWindowSizeIsSmall() && tickerRender()}
            <div className="row bz-control-group bz-editor-stats">
              <span>Character Count: {charCount}</span>
              <span>Word Count: {wordCount}</span>
            </div>
            <Editor
              editorRef={this.setEditorRef}
              mode={editorMode}
              onChange={this.handleChangeBody}
              value={body ? body : this.props.postBoxStory && this.props.postBoxStory.Body}
            />
            <Instructions />
          </div>
          <div className="bz-form-right">
            {!this.isWindowSizeIsSmall() && tickerRender()}
            {this.isWindowSizeIsSmall() && (
              <Collapse ghost>
                <Panel className="pt-label row" header="Meta Details" key="1">
                  {metaRender()}
                </Panel>
              </Collapse>
            )}
            {!this.isWindowSizeIsSmall() && metaRender()}
            <hr />
            <div className="row bz-control-group">
              <Checkbox checked={isBzProPost} inline label="PRO" onChange={this.handleBzProPostChange} />
              <Checkbox checked={isBzPost} inline label="BZ" onChange={this.handleBzPostChange} />
              <Checkbox checked={dnd} inline label="DND" onChange={this.handleDndChange} />
            </div>
            <Select
              className="row"
              isClearable={false}
              onChange={this.handleAuthorChange}
              options={authorOptions}
              placeholder="Author"
              ref={this.authorComboBox}
              value={post_as_newsdesk_user ? authorOptions[0] : authorOptions[1]}
            />
            <Button
              className="row pt-intent-primary right bz-button-post"
              disabled={
                (isNil(title) || title.length < 10) &&
                this.props.postBoxStory &&
                Object.values(this.props.postBoxStory).length === 0
              }
              onClick={this.submitPost}
              text="Post"
            />
          </div>
        </div>
        <Callout intent={Intent.DANGER} text={errorMessage} title="Error" />
      </div>
    );
  }
}

const mapStateToProps = (state: RootState): ReduxState => ({
  ...selectSession(state),
});

export default connect(mapStateToProps, null)(withScreenSizeHooksHOC(PostForm));
