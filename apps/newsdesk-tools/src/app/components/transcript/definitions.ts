import { ColDef } from '@ag-grid-community/core';
import { formatDate, formatTime } from '../../utils/utils';
import { StatusCellRenderer, TranscriptTypeCellRenderer } from './renderers';

// Styles for column cells
export const CellStyle = {
  fontSize: '12px',
  fontWeight: 400,
};

// Format datetime strings for display
export const formatDateTime = (dateTimeString: string): string => {
  if (!dateTimeString) return '';

  const date = new Date(dateTimeString);

  // Check if date is valid
  if (isNaN(date.getTime())) return dateTimeString;

  return date.toLocaleString('en-US', {
    day: 'numeric',
    hour: 'numeric',
    hour12: true,
    minute: '2-digit',
    month: 'short',
    year: 'numeric',
  });
};

// Column definitions for the conference call list grid
export const getColumnDefinitions = (): ColDef[] => [
  {
    field: 'createdAt',
    filter: true,
    headerName: 'Date',
    sortable: true,
    valueFormatter: params => formatDate(params.value),
  },
  {
    field: 'startTime',
    filter: true,
    headerName: 'Time',
    sortable: true,
    valueFormatter: params => formatTime(params.value),
  },
  {
    cellStyle: CellStyle,
    field: 'ticker',
    filter: true,
    headerName: 'Ticker',
    sortable: true,
  },
  {
    field: 'companyName',
    filter: true,
    flex: 1,
    headerName: 'Company',
    sortable: true,
  },
  {
    field: 'exchange',
    filter: true,
    headerName: 'Exchange',
    sortable: true,
  },
  {
    field: 'callTitle',
    filter: true,
    flex: 1.5,
    headerName: 'Call Title',
    sortable: true,
  },
  {
    field: 'headline',
    filter: true,
    flex: 2,
    headerName: 'Headline',
    sortable: true,
  },
  {
    cellClass: 'centered-cell',
    cellRenderer: StatusCellRenderer,
    field: 'status',
    filter: true,
    headerClass: 'centered-header',
    headerName: 'Status',
    sortable: true,
    width: 100,
  },
  {
    field: 'duration',
    filter: true,
    headerName: 'Duration (s)',
    sortable: true,
  },
  {
    cellClass: 'centered-cell',
    cellRenderer: TranscriptTypeCellRenderer,
    field: 'transcriptType',
    filter: true,
    headerClass: 'centered-header',
    headerName: 'Transcript Type',
    sortable: true,
    width: 120,
  },
  {
    field: 'startTime',
    filter: true,
    headerName: 'Start Time',
    sortable: true,
    valueFormatter: params => formatTime(params.value),
  },
  {
    field: 'endTime',
    filter: true,
    headerName: 'End Time',
    sortable: true,
    valueFormatter: params => (params.value ? formatTime(params.value) : 'N/A'),
  },
  {
    field: 'createdAt',
    filter: true,
    headerName: 'Created At',
    hide: true,
    sortable: true,
    valueFormatter: params => formatDateTime(params.value),
  },
  {
    field: 'updatedAt',
    filter: true,
    headerName: 'Updated At',
    hide: true,
    sortable: true,
    valueFormatter: params => formatDateTime(params.value),
  },
];

// Default column definition
export const getDefaultColumnDefinition = () => ({
  cellStyle: CellStyle,
  flex: 1,
  // Add custom header styles
  headerComponentParams: {
    template: `
      <div class="ag-cell-label-container" role="presentation">
        <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>
        <div ref="eLabel" class="ag-header-cell-label" role="presentation">
          <span ref="eText" class="ag-header-cell-text" role="columnheader" style="font-weight: 500; font-size: 12px; color: #5c7080;"></span>
          <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon"></span>
          <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order"></span>
          <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon"></span>
          <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon"></span>
          <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon"></span>
        </div>
      </div>
    `,
  },
  minWidth: 100,
  resizable: true,
});
