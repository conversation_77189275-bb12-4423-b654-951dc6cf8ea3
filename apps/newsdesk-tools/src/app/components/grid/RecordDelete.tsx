import * as React from 'react';
import { ReactElement } from 'react';

import { <PERSON>ton, Dialog, IconName, Intent } from '@blueprintjs/core';
import { Tooltip } from 'antd';

interface Props {
  className?: string;
  disabled: boolean;
  icon: IconName;
  intent: Intent;
  minimal: boolean;
  tabIndex?: number;
  title?: string;
  toolTip?: string;
  text?: string;
  onConfirm(): void;
}

interface State {
  showDeleteConfirmation: boolean;
}

export default class RecordDelete extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);

    this.state = {
      showDeleteConfirmation: false,
    };
  }

  handleClick = (): void => {
    this.setState({ showDeleteConfirmation: true });
  };
  onCancel = (): void => {
    this.setState({ showDeleteConfirmation: false });
  };

  handleConfirm = (): void => {
    this.props.onConfirm();
    this.setState({ showDeleteConfirmation: false });
  };

  render(): ReactElement<HTMLDivElement> {
    const { className, disabled, icon, intent, minimal, tabIndex, text, title, toolTip } = this.props;

    const { showDeleteConfirmation } = this.state;

    const renderConfirmMessage = <span>Are you sure that you want to delete this row?</span>;

    const renderContent = <div className="bp5-dialog-body">{renderConfirmMessage}</div>;

    const renderActionButtons = (
      <div className="bp5-dialog-footer">
        <div className="bp5-dialog-footer-actions">
          <Button intent={Intent.DANGER} onClick={this.handleConfirm} text="Yes" />
          <Button intent={Intent.NONE} onClick={this.onCancel} text="No" />
        </div>
      </div>
    );

    const renderPopup = (
      <Dialog
        className="bz-alert-dialog"
        icon="ban-circle"
        isOpen={showDeleteConfirmation}
        onClose={this.onCancel}
        title="Confirmation"
      >
        {renderContent}
        {renderActionButtons}
      </Dialog>
    );

    return (
      <React.Fragment>
        <Tooltip placement="bottom" title={toolTip ? toolTip : 'Delete Row'}>
          <Button
            className={className}
            disabled={disabled}
            icon={icon}
            intent={intent}
            minimal={minimal}
            onClick={this.handleClick}
            tabIndex={tabIndex}
            text={text}
            title={title}
          />
        </Tooltip>
        {renderPopup}
      </React.Fragment>
    );
  }
}
