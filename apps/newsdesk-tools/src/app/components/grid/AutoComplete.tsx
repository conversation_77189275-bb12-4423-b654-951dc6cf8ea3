import * as React from 'react';
import { ChangeEvent, Component, createRef, RefObject } from 'react';

import { Column, GridApi, RowNode } from '@ag-grid-community/core';
import Downshift from 'downshift';
import { includes, isEmpty } from 'lodash';
import { equals, filter, find, map } from 'ramda';
import { isArray, isUndefined, mapIndexed } from 'ramda-adjunct';

import { loadAutoComplete } from '../../services/autocomplete';
import { CollectionId } from '../collections/collectionEntities';
import { Keys } from '@blueprintjs/core';

type AutoCompleteFieldData = string;
// TODO Make enum for all autocomplete dictionaries
type Dictionary = string;

interface AutocompleteItem {
  data: TickerData | AutoCompleteFieldData;
  label: string;
  value: string;
}

interface AutoFill {
  fill?: string;
  name: string;
  priorityFill?: string;
}

interface TickerData {
  country: string;
  currency: string;
  exchange?: string;
  isin?: string;
  name?: string;
  shortName: string;
  symbol?: string;
  type?: string;
}

interface AutocompleteValue {
  id: string;
  name: string;
}

interface State {
  autocompleteItems: AutocompleteItem[];
  highlightedIndex: number;
  id: string | null;
  isOpen: boolean;
  query: string;
  selectedItem: TickerData | string | AutocompleteValue[];
}

type Value = string | AutocompleteValue[];

interface Props {
  api: GridApi;
  alwaysOpen?: boolean;
  autoComplete?: AutocompleteItem[];
  autoFill?: AutoFill[];
  column: Column;
  collectionId?: string;
  dictionary?: Dictionary;
  field?: string;
  id: string | null;
  node: RowNode;
  value: Value;
}

const ACQUIRER_TICKER = 'acquirer_ticker';
const TARGET_TICKER = 'target_ticker';
const TICKER = 'ticker';

class AutoComplete extends Component<Props, State> {
  private input: RefObject<HTMLInputElement>;

  constructor(props: Props) {
    super(props);
    this.state = {
      autocompleteItems: [],
      highlightedIndex: -1,
      id: props.column.getColId(),
      isOpen: false,
      query: '',
      selectedItem: '',
    };
    this.input = createRef();
  }

  componentDidMount() {
    const { autoComplete, value } = this.props;

    let modifiedValue = value;

    if (!isUndefined(value) && !isEmpty(value)) {
      if (isArray(value)) {
        modifiedValue = value[0].name;
      } else if (equals(typeof value, 'object')) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        modifiedValue = value;
      }
    }
    this.afterGuiAttached();
    const autocompleteItems = autoComplete && autoComplete.length > 0 ? autoComplete : [];
    this.setState({
      autocompleteItems,
      query: modifiedValue as string,
      selectedItem: modifiedValue,
    });
  }

  afterGuiAttached() {
    if (this.input && this.input.current) {
      this.input.current.focus();
      setTimeout(() => this.input.current && this.input.current.select(), 0);
    }
  }

  buildNodeData(autoCompleteItems: AutocompleteItem[]) {
    const { autoFill, column, node } = this.props;
    const { highlightedIndex, query, selectedItem } = this.state;
    const { data } = node;
    const autoFillData = { ...data };

    // In case of Manual ticker entry
    if (autoCompleteItems.length <= 0 && equals(selectedItem, '')) {
      map<AutoFill, void>(item => (autoFillData[item.name] = ''), autoFill);
    } else if (autoCompleteItems.length > 0) {
      const isTickerField =
        equals(column.getColDef().field, TICKER) ||
        equals(column.getColDef().field, ACQUIRER_TICKER) ||
        equals(column.getColDef().field, TARGET_TICKER);

      let autoCompleteData: AutocompleteItem = find(u => equals(u.label, selectedItem), autoCompleteItems);

      if (isTickerField) {
        autoCompleteData = find(({ data }: AutocompleteItem) => {
          const typedData = data as TickerData;
          const typedSelectedItem = selectedItem as TickerData;
          return (
            equals(typedData.symbol, typedSelectedItem.symbol) && equals(typedData.exchange, typedSelectedItem.exchange)
          );
        }, autoCompleteItems as AutocompleteItem[]);
      }

      if (
        isTickerField &&
        highlightedIndex <= 0 &&
        !equals(autoCompleteItems[0].label.toUpperCase(), query.toUpperCase())
      ) {
        map<AutoFill, void>(item => (autoFillData[item.name] = ''), autoFill);
      } else if (!isEmpty(autoCompleteData)) {
        const { data } = autoCompleteData;
        map<AutoFill, void>(item => {
          autoFillData[item.name] = data[item.priorityFill];
          if (isEmpty(data[item.priorityFill])) {
            autoFillData[item.name] = data[item.fill] || data[item.name];
          }
        }, autoFill);
      }
    }
    return autoFillData;
  }

  isPopup() {
    return true;
  }

  getValue() {
    let nodeData;
    const { autoFill, collectionId, column, node } = this.props;
    const { autocompleteItems, highlightedIndex, query, selectedItem } = this.state;

    if (!isEmpty(autoFill)) {
      nodeData = this.buildNodeData(autocompleteItems);
      node.setData(nodeData);
    }

    const isTickerField =
      equals(column.getColDef().field, TICKER) ||
      equals(column.getColDef().field, ACQUIRER_TICKER) ||
      equals(column.getColDef().field, TARGET_TICKER);

    if (isTickerField) {
      const selectedValue = selectedItem as TickerData;
      const value =
        highlightedIndex <= 0
          ? collectionId && collectionId === CollectionId.wiim
            ? ''
            : query.toUpperCase()
          : selectedValue.symbol;
      return value;
    }
    return selectedItem;
  }

  handleChange = event => {
    this.setState({
      id: event.data._id,
      query: event.value,
      selectedItem: event.data,
    });
  };

  handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { autoComplete, dictionary, field = '' } = this.props;
    const query = event.target.value;
    this.setState({ query });

    if (isEmpty(query)) {
      if (equals(field, 'drug')) {
        this.setState({
          autocompleteItems: [],
          selectedItem: null,
        });
      } else {
        this.setState({
          autocompleteItems: [],
          selectedItem: '',
        });
      }
    } else if (!isUndefined(autoComplete)) {
      const autocompleteItems = filter(item => includes(item.value.toLowerCase(), query.toLowerCase()), autoComplete);
      const selectedItem = !isEmpty(autocompleteItems) ? autocompleteItems[0].data : '';

      this.setState({
        autocompleteItems,
        selectedItem,
      });
    } else {
      loadAutoComplete(
        dictionary,
        field,
      )(event.target.value)
        .then(res => {
          const { options } = res;
          const id = !isEmpty(options) ? options[0].data._id : null;
          const selectedItem = !isEmpty(options) ? options[0].data : '';

          this.setState({
            autocompleteItems: res.options,
            id,
            selectedItem,
          });
        })
        .catch(error => {
          throw new Error(error);
        });
    }
  };

  stateChanged = (changes, state) => {
    const {
      stateChangeTypes: { clickItem, keyDownEnter },
    } = Downshift;
    this.setState({ isOpen: state.isOpen });
    if (!equals(changes.type, keyDownEnter) && !equals(changes.type, clickItem)) {
      this.setState({ highlightedIndex: state.highlightedIndex });
    }
  };

  selectHighlightedItem = (e): string => {
    if (e) {
      this.setState({
        id: e.data._id,
        selectedItem: e.data,
      });
    }
    return e ? e.value : '';
  };

  handleNavigationKeyPress = (keyCode: number) => {
    const { api } = this.props;
    const { column, rowIndex } = api.getFocusedCell();
    const rowCount = api.getDisplayedRowCount();
    switch (keyCode) {
      case Keys.ARROW_UP: {
        if (!equals(rowIndex, 0)) {
          api.stopEditing();
          api.setFocusedCell(rowIndex - 1, column);
        }
        break;
      }
      case Keys.ARROW_DOWN: {
        if (!equals(rowIndex, rowCount - 1)) {
          api.stopEditing();
          api.setFocusedCell(rowIndex + 1, column);
        }
        break;
      }
    }
  };

  handleKeyDown = event => {
    const { keyCode } = event;
    const openStatus = this.props.alwaysOpen ? true : this.state.isOpen;

    if (
      (!openStatus || isEmpty(this.state.autocompleteItems)) &&
      (equals(keyCode, ARROW_UP) || equals(keyCode, ARROW_DOWN))
    ) {
      this.handleNavigationKeyPress(keyCode);
      event.nativeEvent.preventDownshiftDefault = true;
      event.preventDefault();
    }
  };

  render() {
    const width = this.props.column.getActualWidth() - 8;
    const { autocompleteItems, id, isOpen, query } = this.state;
    const { alwaysOpen } = this.props;

    let definedValue = query;

    if ((equals(id, 'trader') || equals(id, 'drug') || equals(id, 'companies')) && !isEmpty(query)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore FIXME: Improve the types of the query
      definedValue = query.name;
    }

    const openStatus = alwaysOpen || isOpen;
    return (
      <Downshift
        defaultHighlightedIndex={0}
        itemToString={this.selectHighlightedItem}
        onChange={this.handleChange}
        onStateChange={this.stateChanged}
      >
        {({ getInputProps, getItemProps, getLabelProps, getMenuProps, highlightedIndex, selectedItem }) => (
          <div className="downshift-parent-div">
            <label {...getLabelProps({ style: { display: 'none' } })} />
            <div className="downshift-child-div">
              <input
                className="downshift-input"
                ref={this.input}
                style={{ minWidth: '0', width }}
                {...getInputProps({
                  onChange: this.handleInputChange,
                  onKeyDown: this.handleKeyDown,
                  value: definedValue,
                })}
              />
            </div>
            {!isEmpty(autocompleteItems) && !includes(autocompleteItems, selectedItem) && (
              <div className="downshift-child-div">
                <ul className="downshift-ul" {...getMenuProps()}>
                  {openStatus
                    ? mapIndexed<AutocompleteItem, JSX.Element>((item, index) => {
                        const tickerData = item.data as TickerData;
                        const key = !isUndefined(tickerData.exchange)
                          ? `${item.value}-${tickerData.exchange}`
                          : item.value;

                        const { country, exchange, name, shortName } = tickerData;

                        const companyName = !isEmpty(shortName) ? shortName : name;

                        const value = !isUndefined(exchange)
                          ? `${item.value} (${exchange}, ${country}) | ${companyName}`
                          : item.value;

                        const backgroundColor = equals(highlightedIndex, index) ? 'lightgray' : null;
                        const fontWeight = equals(selectedItem, item) ? 'bold' : 'normal';

                        return (
                          <li
                            {...getItemProps({
                              className: 'downshift-list',
                              index,
                              item,
                              key,
                              style: {
                                backgroundColor,
                                fontWeight,
                              },
                            })}
                          >
                            {value}
                          </li>
                        );
                      }, autocompleteItems)
                    : null}
                </ul>
              </div>
            )}
          </div>
        )}
      </Downshift>
    );
  }
}

export default AutoComplete;
