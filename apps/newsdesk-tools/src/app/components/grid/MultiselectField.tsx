import { equals, isNil } from 'ramda';
import * as React from 'react';
import { Component, createRef, KeyboardEvent } from 'react';

import { Column, GridApi, RowNode } from '@ag-grid-community/core';
import { isArray, isEmpty, isEqual, map } from 'lodash';
import { isUndefined } from 'ramda-adjunct';

import AsyncSelect from 'react-select/async';

import { autoCompleteOptions, loadAutoComplete, tagsOptions } from '../../services/autocomplete';
import { CollectionId } from '../collections/collectionEntities';
import { Keys } from '@blueprintjs/core';

interface AutoFill {
  name: string;
  fill?: string;
}

interface Item {
  _id: string;
  name?: string;
  full_name?: string;
}

interface SelectedItem {
  data: Item;
  label: string;
  value: string;
}

interface State {
  isOpen: boolean;
  selectedItems: SelectedItem | SelectedItem[];
}

interface Props {
  api: GridApi;
  arrayType?: string;
  collectionId?: string;
  autoFill?: AutoFill[];
  column: Column;
  dictionary: string;
  field?: string;
  fullDetails?: boolean;
  id: string | null;
  isMulti: boolean;
  itemField?: string;
  filterField?: string;
  node: RowNode;
  value: Item | Item[] | string[];
}

// TODO: Needs to improve component
class MultiselectField extends Component<Props, State> {
  private input: any = createRef();

  constructor(props: Props) {
    super(props);

    this.state = {
      isOpen: false,
      selectedItems: [],
    };
  }

  buildItems = (item, field, obj = null) => {
    return {
      data: obj || item,
      label: field ? item[field] : item,
      value: field ? item[field] : item,
    };
  };

  componentDidMount() {
    const { arrayType, itemField, value } = this.props;
    if (!isUndefined(value) && !isEmpty(value)) {
      const selectedItems = isArray(value)
        ? isEqual(arrayType, 'string')
          ? map(value as Item[], item => this.buildItems(item, null, { symbol: item }))
          : map(value as Item[], item => this.buildItems(item, itemField))
        : this.buildItems(value as Item, itemField);
      this.setState({ selectedItems });
    }
    this.afterGuiAttached();
  }

  afterGuiAttached() {
    if (this.input && this.input.current) {
      this.input.current.focus();
    }
  }

  findFilterValue() {
    const { filterField, node } = this.props;
    const { data } = node;
    const autoFillData = { ...data };
    if (filterField && !isEmpty(autoFillData[filterField])) return autoFillData[filterField];
  }

  buildNodeData(selectedItem) {
    const { autoFill, node } = this.props;
    const { data } = node;
    const autoFillData = { ...data };

    if (isEmpty(autoFill)) {
      return;
    }

    if (isEmpty(selectedItem)) {
      map(autoFill, item => (autoFillData[item.name] = ''));
    } else {
      const { data } = selectedItem;
      map(autoFill, item => (autoFillData[item.name] = data[item.fill] || data[item.name]));
    }
    node.setData(autoFillData);
  }

  getValue() {
    const { selectedItems } = this.state;
    const { collectionId, value } = this.props;

    if (isEmpty(selectedItems)) {
      if (this.props.isMulti) {
        return [];
      } else {
        if (!isNil(value)) {
          this.buildNodeData(selectedItems);
        }
        return null;
      }
    }
    if (!this.props.isMulti && !isArray(selectedItems)) {
      if (!equals(selectedItems.data, value)) {
        this.buildNodeData(selectedItems);
      }
      return selectedItems.data;
    }

    if ((this.props.isMulti && this.props.fullDetails) || (collectionId && collectionId === CollectionId.event)) {
      if (!isArray(selectedItems)) {
        //hack to fix single selections
        const newSelectedItems: SelectedItem[] = [];
        newSelectedItems.push(selectedItems);
        return map(newSelectedItems as SelectedItem[], item => item.data);
      } else {
        if ((selectedItems as SelectedItem[]).length > 0) {
          this.buildNodeData(selectedItems[0]);
        }
        return map(selectedItems as SelectedItem[], item => item.data);
      }
    }
    return map(selectedItems as SelectedItem[], item => item.data);
  }

  isPopup() {
    return true;
  }

  handleChange = (selectedItems: SelectedItem[] | SelectedItem) => {
    this.setState({ selectedItems });
  };

  handleMenuOpen = () => {
    this.setState({ isOpen: true });
  };

  handleMenuClose = () => {
    this.setState({ isOpen: false });
  };

  handleNavigationKeys = (event: KeyboardEvent<HTMLElement>) => {
    if (this.state.isOpen) {
      return;
    }
    const { api } = this.props;
    const { keyCode } = event;
    const { column, rowIndex } = api.getFocusedCell();
    const rowCount = api.getDisplayedRowCount();
    switch (keyCode) {
      case Keys.ARROW_UP: {
        if (!equals(rowIndex, 0)) {
          api.stopEditing();
          api.setFocusedCell(rowIndex - 1, column);
          event.preventDefault();
        }
        break;
      }
      case Keys.ARROW_DOWN: {
        if (!equals(rowIndex, rowCount - 1)) {
          api.stopEditing();
          api.setFocusedCell(rowIndex + 1, column);
          event.preventDefault();
        }
        break;
      }
    }
  };

  autoCompleteOptionsWithDetails = (dictionary, field) => inputValue =>
    loadAutoComplete(
      dictionary,
      field,
    )(inputValue).then(res => {
      const { options } = res;
      return options.map(item => {
        const companyName = !isEmpty(item.data.shortName) ? item.data.shortName : item.data.name;
        const label = !isUndefined(item.data.exchange)
          ? `${item.value} (${item.data.exchange}, ${item.data.country}) | ${companyName}`
          : item.value;
        return { data: item.data, label: label, value: item.value };
      });
    });

  render() {
    const { column, dictionary, field = '', filterField, fullDetails, isMulti } = this.props;
    const { selectedItems } = this.state;
    const customStyles = {
      control: styles => ({
        ...styles,
        width: column.getActualWidth() - 1,
      }),
    };

    const options = filterField
      ? autoCompleteOptions(dictionary, field, this.findFilterValue(), filterField)
      : dictionary === 'tags'
        ? tagsOptions
        : fullDetails
          ? this.autoCompleteOptionsWithDetails(dictionary, field)
          : autoCompleteOptions(dictionary, field);

    return (
      <AsyncSelect
        isClearable
        isMulti={isMulti}
        loadOptions={options}
        onChange={this.handleChange}
        onKeyDown={this.handleNavigationKeys}
        onMenuClose={this.handleMenuClose}
        onMenuOpen={this.handleMenuOpen}
        placeholder={''}
        ref={this.input}
        styles={customStyles}
        value={selectedItems}
      />
    );
  }
}

export default MultiselectField;
