import {
  ColDef,
  Column,
  CsvExportParams,
  ProcessCellForExportParams,
  ProcessHeaderForExportParams,
  SideBarDef,
  ValueGetterParams,
  ValueSetterParams,
} from '@ag-grid-community/core';
import moment from 'moment';
import {
  contains,
  defaultTo,
  equals,
  filter,
  isEmpty,
  isNil,
  join,
  map,
  pipe,
  prop,
  replace,
  split,
  without,
} from 'ramda';
import { isArray, isNaN, isNull, isUndefined } from 'ramda-adjunct';
import * as React from 'react';

import { DateField, RowState, UserCursors } from '../../reducers/entities/collectionEntities';
import ErrorTooltip from './ErrorTooltip';
import UserPositionToolTip from './UserPositionToolTip';

import { numberShorthand } from '@benzinga/fission';
import { expand } from '../../utils/earningsUtils';
import { CollectionId } from '../collections/collectionEntities';
import { DateTime } from 'luxon';
import { Keys } from '@blueprintjs/core';

const USER_POSITION_BACKGROUND_COLOR = {
  'background-color': 'rgba(57,166,255, 0.3) !important',
};

export const suppressKeyboardNavigation = {
  supressAutocomplete: ({ editing, event }) => {
    if (!editing || event.keyCode === Keys.TAB || event.keyCode === Keys.SHIFT + Keys.TAB) {
      return false;
    }
    return true;
  },
  supressEnter: ({ editing, event }) => editing && event.keyCode === Keys.ENTER,
};

export const rowStyle = {
  addUserPositionBackGround: params => {
    const { data } = params;
    if (data && !(isEmpty(data.usersPosition) || isNil(data.usersPosition))) {
      return USER_POSITION_BACKGROUND_COLOR;
    }
  },
};

export const cellStyle = {
  addCellBackground: params => {
    const {
      colDef: { field },
      data,
    } = params;
    if (data && !(isEmpty(data.errors) || isNil(data.errors)) && data.errors[field] && !data[field]) {
      return {
        'background-color': '#F6DCDB',
        border: '1px solid #DB3737',
      };
    }
    if (equals(data.collectionId, CollectionId.refinitivEstimates)) {
      return cellStyle.addCellBackgroundColor(params);
    }
    if (field && data && data.updated_fields) {
      const hasValueChanged = contains(field, without([DateField.UPDATED], data.updated_fields));
      if (hasValueChanged && (data.state === RowState.REVISION || data.state === RowState.CURRENT_REVISION)) {
        return {
          'background-color': 'rgba(13, 128, 80, 0.5)',
        };
      }
    }
    return {
      'background-color': 'transparent',
      border: '0px',
    };
  },
  addCellBackgroundColor: params => {
    const { date, period } = params.data;
    const todayDate = DateTime.now().toFormat('yyyy-MM-dd');
    if (todayDate === date) {
      return {
        'background-color': '#ADD8E6',
      };
    }
    if (period === 'FY') {
      return {
        'background-color': '#FFF0F5',
      };
    }
  },
};

export const editable = {
  isEditable: ({
    data: {
      permissionSet: { permissions },
      state,
    },
  }) => permissions.canEditRow && state !== RowState.REVISION && state !== RowState.CURRENT_REVISION,
};

const renderCellWithToolTips = (data, field, value) => {
  const hasErrors = data && data.errors && data.errors[field];
  const { usersPosition } = data;
  let users = [];

  if (!(isEmpty(usersPosition) || isNil(usersPosition))) {
    users = filter<UserCursors>(user => equals(user.row_id, data._id) && equals(user.field, field), usersPosition);
  }

  let definedValue = value;

  if (equals(field, 'securities')) {
    if (data.securities) {
      const tickers = map(symbol => symbol.symbol, data && data.securities);
      definedValue = join(', ')(tickers);
    }
  } else if (equals(field, 'trader') || (equals(field, 'drug') && !isNull(value)) || equals(field, 'companies')) {
    definedValue = value?.name ?? '';
  }

  if (
    (equals(data.collectionId, 'drugs') && equals(field, 'companies')) ||
    (equals(data.collectionId, 'fda') && equals(field, 'companies'))
  ) {
    definedValue = join(', ')(value);
  }

  return (
    <div className="grid-cell-renderer">
      {definedValue}
      {hasErrors && <ErrorTooltip errorContent={data.errors[field]} />}
      {!(isEmpty(users) || isNil(users)) && <UserPositionToolTip users={users} />}
    </div>
  );
};

export const cellRenderer = {
  joinArrayField:
    (joinField = '') =>
    ({ colDef: { field }, data, value }) => {
      let renderValue;

      if (equals(joinField, 'drugs_companies')) {
        renderValue = map(prop('name'))(value);
      } else {
        renderValue = pipe(defaultTo([]), map(prop(joinField)), join(', '))(value);
      }

      return renderCellWithToolTips(data, field, renderValue);
    },

  renderCheckbox: ({ data, value }) => {
    const num = numberShorthand(value);
    return (
      <div>
        <input
          onChange={() => {
            data = { ...data, field: !data.field };
          }}
          type="checkbox"
        />
        &nbsp;&nbsp;&nbsp;{num}
      </div>
    );
  },

  renderFullName: ({ colDef: { field }, data, value }) => {
    const fullName = isEmpty(value) || isNil(value) ? '' : value.full_name;
    return renderCellWithToolTips(data, field, fullName);
  },

  renderNameWithValidation: ({ colDef: { field }, data, value }) => {
    const name = isEmpty(value) || isNil(value) ? '' : value.name;
    return renderCellWithToolTips(data, field, name);
  },

  renderShortHandWithValidation: ({ colDef: { field }, data, value }) => {
    const num = numberShorthand(value);
    return renderCellWithToolTips(data, field, num);
  },

  renderValidationError: ({ colDef: { field }, data, value }) => renderCellWithToolTips(data, field, value),

  stringifyTickers: ({ colDef: { field }, data, value }) => {
    const renderValue = pipe(join(', '), defaultTo(''))(value || []);
    return renderCellWithToolTips(data, field, renderValue);
  },
};

export const valueGetter = {
  booleanGetterHOF: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    return value ? 'Y' : 'N';
  },
  booleanWithNullGetterHOF: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    if (isNull(value)) {
      return '';
    }
    return value ? 'Y' : 'N';
  },
  dateFromMillies: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    if (value !== null && typeof value === 'number') {
      return moment.unix(value).format('YYYY-MM-DD HH:mm:ss');
    } else return value;
  },
  dateHOF: (field: string, format?: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    return moment.unix(value).format(format || 'YYYY-MM-DD');
  },
  dateTimeHOF: (field: string, format?: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    return moment.unix(value).format(format || 'YYYY-MM-DD HH:mm:ss');
  },
  getArrayFieldObjectNameValue: (field: string) => (params: ValueGetterParams) => {
    const dataArray = params.data[field];

    if (isNull(dataArray)) {
      return '';
    }
    const dataNames = dataArray.map(data => data.name);
    return dataNames;
  },
  getFieldObjectNameValue: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    if (isNull(value)) {
      return '';
    }
    return value.name;
  },
  getNumberShorthand: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    const num = numberShorthand(value);
    return num !== '0' ? num : '';
  },
  getNumberShorthandForRevSurprise: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    return numberShorthand(value);
  },
  nullGetterHOF: (field: string) => (params: ValueGetterParams) => {
    const value = params.data[field];
    return isEmpty(value) || isNil(value) ? null : value;
  },
};

export const valueParser = {
  floatParser: ({ newValue }) => {
    const parsedValue = parseFloat(newValue);
    if (isNaN(parsedValue)) {
      return null;
    }
    return parsedValue;
  },
  floatParserWithFixed: ({ newValue }) => {
    const parsedValue = parseFloat(newValue).toFixed(3);
    if (isNaN(parsedValue) || isEmpty(newValue) || isNaN(newValue)) {
      return null;
    }
    return parsedValue;
  },
  integerParser: ({ newValue }) => {
    const parsedValue = parseInt(newValue, 10);
    if (isNaN(parsedValue)) {
      return null;
    }
    return parsedValue;
  },
  upperCase: ({ newValue }) => newValue.toUpperCase(),
};

export const valueSetter = {
  booleanSetter:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      data[field] = equals(newValue, 'Y');
      return true;
    },
  booleanWithNullSetter:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      if (isEmpty(newValue) || isNil(newValue)) {
        data[field] = null;
      } else {
        data[field] = equals(newValue, 'Y');
      }
      return true;
    },
  dateSetter:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      data[field] = newValue;
      return true;
    },
  periodSetter:
    (field: string) =>
    ({ data, newValue, oldValue }: ValueSetterParams): boolean => {
      if (equals(oldValue.toLowerCase(), newValue.toLowerCase())) {
        return false;
      }
      let valueToSet = '';
      switch (newValue) {
        case '1':
          valueToSet = 'Q1';
          break;
        case '2':
          valueToSet = 'Q2';
          break;
        case '3':
          valueToSet = 'Q3';
          break;
        case '4':
          valueToSet = 'Q4';
          break;
      }
      if (!isEmpty(valueToSet) && equals(valueToSet, oldValue)) {
        return false;
      }
      data[field] = newValue;
      return true;
    },
  releaseSetter:
    (field: string) =>
    ({ data, newValue, oldValue }: ValueSetterParams) => {
      if (equals(oldValue.toLowerCase(), newValue.toLowerCase())) {
        return false;
      }
      data[field] = newValue.toUpperCase();
      return true;
    },
  setIndicationSymptoms:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      const commaSeparatedValues = split(',')(newValue);
      data[field] = commaSeparatedValues;
      return true;
    },
  setShortHandField:
    (field: string) =>
    ({ data, newValue, oldValue }: ValueSetterParams) => {
      const expandedValue = expand(newValue);
      const isValueChanged = !equals(expand(oldValue), expandedValue);
      if (isValueChanged) {
        data[field] = isNaN(expandedValue) ? newValue : expandedValue != null ? expandedValue.toString() : null;
      }
      return isValueChanged;
    },
  stringWithNullSetter:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      if (equals(newValue, '')) {
        return false;
      }
      data[field] = newValue;
      return true;
    },
  tickersSetter:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      if (equals(field, 'securities')) {
        const fieldData = map(
          ({ country, cusip, exchange, isin, symbol }) => ({
            country,
            cusip,
            exchange,
            isin,
            symbol,
          }),
          newValue,
        );

        data[field] = fieldData;
      } else {
        data[field] = map(item => item.symbol, newValue);
      }

      return true;
    },
  traderSetter:
    (field: string) =>
    ({ data, newValue }: ValueSetterParams) => {
      data[field] = newValue;
      return true;
    },
};

export const columnDefinitionModifier = {
  addRowNumberColumn: (columnDefinitions: ColDef[]): ColDef[] => [
    {
      editable: false,
      initialHide: true,
      valueGetter: params => {
        const index = params.node.rowIndex + 1;
        return index.toString();
      },
      width: 50,
    },
    ...columnDefinitions,
  ],
};

export const filterParams = {
  dateComparator: {
    comparator(filterDateValue, cellDateValue): number {
      if (moment(filterDateValue).isSame(cellDateValue)) {
        return 0;
      } else if (moment(cellDateValue).isBefore(filterDateValue)) {
        return -1;
      } else if (moment(cellDateValue).isAfter(filterDateValue)) {
        return 1;
      }
      // this should not occur.
      return -0;
    },
    newRowsAction: 'keep',
  },
};

export const columnTypes = {
  'action-field': { minWidth: 110 },
  'numeric-field': { cellClass: 'numeric-cell' },
  'width-large': { maxWidth: 400 },
  'width-medium': { maxWidth: 200 },
  'width-small': { maxWidth: 100 },
  'width-small-hidden-field': { maxWidth: 100, width: 100 },
};

export const sideBar = {
  showToolPanels: (isRevision: boolean) => {
    const panes: SideBarDef = {
      toolPanels: [
        {
          iconKey: 'columns',
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          toolPanel: 'agColumnsToolPanel',
          toolPanelParams: {
            suppressPivotMode: true,
            suppressRowGroups: true,
            suppressValues: true,
          },
        },
        ...(isRevision
          ? []
          : [
              {
                iconKey: 'filter',
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                toolPanel: 'agFiltersToolPanel',
              },
            ]),
      ],
    };

    return panes;
  },
};

export const defaultColumnDefinition: ColDef = {
  cellClass: 'default-cell',
  cellRenderer: cellRenderer.renderValidationError,
  cellStyle: cellStyle.addCellBackground,
  editable: editable.isEditable,
  filterParams: { newRowsAction: 'keep' },
  resizable: true,
  sortable: true,
};

function getNonHiddenColumnsExceptAction(columns: Column[]): string[] {
  let keys = map((column: Column) => {
    const colDef = column.getColDef();
    if (equals(colDef.headerName, 'Action')) {
      return;
    }
    return colDef.field;
  }, columns);
  keys = filter(value => value !== undefined, keys);
  return keys;
}

function processCells({ column, value }: ProcessCellForExportParams) {
  if (
    column.getColDef().cellEditor &&
    !isUndefined(column.getColDef().cellEditorParams) &&
    !isUndefined(column.getColDef().cellEditorParams.isMulti)
  ) {
    if (isEmpty(value) || isNil(value)) {
      return value;
    }
    if (isArray(value)) {
      return map(obj => obj.name || obj.full_name || obj.symbol || obj, value);
    } else if (typeof value === 'object') {
      return value.name || value.full_name;
    }
  }

  if (equals(column.getColDef().field, 'drug')) {
    return value && value.name;
  }

  return value;
}

function processHeaders({ column }: ProcessHeaderForExportParams) {
  return replace(/\n/, ' ', column.getColDef().headerName);
}

export function exportToCSV(columns: Column[], fileName: string): CsvExportParams {
  return {
    allColumns: false,
    columnKeys: getNonHiddenColumnsExceptAction(columns),
    columnSeparator: ',',
    // customFooter: '\nExported from Newsdesk Tools',
    fileName: `${fileName}.csv`,
    onlySelected: false,
    onlySelectedAllPages: false,
    processCellCallback: processCells,
    processHeaderCallback: processHeaders,
    skipColumnGroupHeaders: false,
    skipColumnHeaders: false,
    suppressQuotes: false,
  };
}

export const isPrimaryAutoCompleteItems = [
  { data: 'Y', label: 'Y', value: 'Y' },
  { data: 'N', label: 'N', value: 'N' },
];
