import { Button, InputGroup } from '@blueprintjs/core';
import * as React from 'react';
import { Component, FormEvent } from 'react';
import { login } from '../actions/session';

interface State {
  password: string;
  username: string;
}

interface Props {
  errorMessage?: string;
  loggingIn: boolean;
  onSubmit: typeof login;
}

export default class LoginForm extends Component<Props, State> {
  readonly state: State = {
    password: '',
    username: '',
  };

  handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    const { password, username } = this.state;
    this.props.onSubmit(username, password);
    event.preventDefault();
  };

  handleUserNameChange = event => {
    this.setState({ username: event.target.value });
  };

  handlePasswordChange = event => {
    this.setState({ password: event.target.value });
  };

  render() {
    const { password, username } = this.state;
    const { errorMessage, loggingIn } = this.props;

    return (
      <div className="container container-center">
        <form className="bz-login bp5-control-group bp5-vertical" onSubmit={this.handleSubmit}>
          <a className="bz-logo" href="/login" />
          <InputGroup leftIcon="person" onChange={this.handleUserNameChange} placeholder="Username" type="text" />
          <InputGroup
            large
            leftIcon="lock"
            onChange={this.handlePasswordChange}
            placeholder="Password"
            type="password"
          />
          <Button
            className="bp5-button bp5-large bp5-intent-primary pt-fill"
            disabled={!username || !password || loggingIn}
            icon="log-in"
            text="Login"
            type="submit"
          />
          {errorMessage && (
            <div className="pt-callout pt-intent-danger">
              <h5>Authentication failed</h5>
              {errorMessage}
            </div>
          )}
        </form>
      </div>
    );
  }
}
