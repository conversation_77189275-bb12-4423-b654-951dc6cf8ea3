import { GridOptions } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';

import { Button, Classes, Dialog, Intent, Overlay } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import { isEmpty } from 'lodash';
import { all, defaultTo, equals, has, isNil, join, map, pipe, prop } from 'ramda';
import { isArray, isBoolean, isNull, isObj, isUndefined, mapIndexed } from 'ramda-adjunct';
import { ReactElement } from 'react';
import * as React from 'react';
import { connect } from 'react-redux';
import { compose, Dispatch } from 'redux';

import { deleteRowAsync, discardRowAsync, publishRowAsync, revertRowAsync } from '../../actions';
import { createCalendarPost } from '../../actions/news';
import { Errors, Row, RowCommand, RowData, RowState } from '../../reducers/entities/collectionEntities';
import { CollectionGroupId, CollectionId, PermissionsByGroupId } from '../collections/collectionEntities';
import RecordDelete from '../grid/RecordDelete';
import history from './historyDefinitions';
import { Tooltip } from 'antd';
import { editCalendarDetails } from '../../actions/edit';

/******************************************************************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 **************************** B E W A R E ! ! ! ! ! ! *************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 ******************************************************************************
 * This file is intended to contain general purpose code that services either Dictionaries or Calendars (or some future kind of collection that doesn't exist at the time of this writing).
 * We all worked very hard to accomplish this (i.e. the codebase wasn't structured like this originally).
 * Doing so brings TONS of benefits that all drammatically streamline the developent of new features (to the grids) and adding new collections/calendars/dictionaries.
 * Please resist the urge to add code to this file that does someting along the lines of:
   ```
   if (type === 'calendar') {
     do(XYZ);
   } else if (type === 'dictionary') {
     do(ABC);
   }
   ```
 * Instead, focus your effort on adding the MINIMUM necessary information to (all) collection definitions for whatever your new feature is.
 * Then, you can add that functionality to this component by looking at those parameters from the collection definition that specifically define how to render the grid with the parameters you created in mind.
 */

export enum BUTTON_KEYS {
  DELETE = 'delete',
  DISCARD = 'discard',
  REVERT = 'revert',
  SAVE = 'save',
  STORY = 'story',
  UPDATE_HISTORY_VIEW = 'update_history_view',
}

interface DispatchableActions {
  onCreatePost(row: Row): void;
  onEditDetail(row: Row): void;
  onRowDelete(collectionId: CollectionId, row: Row): void;
  onRowDiscard(collectionId: CollectionId, row: Row): void;
  onRowPublish(collectionId: CollectionId, row: Row): void;
  onRowRevert(collectionId: CollectionId, rowId: string): void;
}

interface OriginalRowUpdateHistory {
  field: string;
  new_value: string;
  previous_value: string;
  updated_by: string;
}

type ModifiedRowData = RowData & {
  collectionId: CollectionId;
  eps?: string;
  errors: Errors;
  permissionSet: PermissionsByGroupId;
  revenue?: string;
  state: RowState;
  update_history: OriginalRowUpdateHistory[];
};

interface OwnProps {
  collectionGroupId: CollectionGroupId;
  data: ModifiedRowData;
}

interface State {
  showConfirmationDialog: boolean;
  showHistoryModal: boolean;
}

type Props = OwnProps & DispatchableActions;

class CollectionActions extends React.Component<Props, State> {
  private gridOptions: GridOptions;

  constructor(props) {
    super(props);

    this.state = {
      showConfirmationDialog: false,
      showHistoryModal: false,
    };
  }

  handleCommand = (command: RowCommand): void => {
    const { data, onCreatePost, onEditDetail, onRowDelete, onRowDiscard, onRowPublish, onRowRevert } = this.props;

    const modifiedRow: Row = {
      data,
      errors: {},
      state: data.state,
    };

    switch (command) {
      case RowCommand.DELETE:
        onRowDelete(data.collectionId, modifiedRow);
        break;
      case RowCommand.DISCARD:
        onRowDiscard(data.collectionId, modifiedRow);
        break;
      case RowCommand.REVERT:
        onRowRevert(data.collectionId, data._id);
        break;
      case RowCommand.SAVE:
        if (equals(data.collectionId, CollectionId.earning)) {
          const { eps, revenue } = data;
          if ((isEmpty(eps) || isEmpty(revenue)) && !(isEmpty(eps) && isEmpty(revenue))) {
            this.setState({ showConfirmationDialog: true });
            return;
          }
        }
        onRowPublish(data.collectionId, modifiedRow);
        break;
      case RowCommand.WRITE:
        onCreatePost(modifiedRow);
        break;
      case RowCommand.EDIT:
        onEditDetail(modifiedRow);
        break;
      default:
        break;
    }
  };

  handleViewClick = () => {
    this.setState({ showHistoryModal: !this.state.showHistoryModal });
  };

  renderButtons = (data: ModifiedRowData) => {
    const {
      collectionGroupId,
      data: {
        permissionSet: { permissions },
        update_history: updateHistory,
      },
    } = this.props;
    const { state: rowState }: { state: RowState } = data;
    const hasErrors = data && !isEmpty(data.errors);

    const handleRowDelete = (): void => {
      this.handleCommand(RowCommand.DELETE);
    };
    const handleRowSave = (): void => {
      this.handleCommand(RowCommand.SAVE);
    };
    const handleRowRevert = (): void => {
      this.handleCommand(RowCommand.REVERT);
    };
    const handleRowDiscard = (): void => {
      this.handleCommand(RowCommand.DISCARD);
    };
    const handleStoryWrite = (): void => {
      this.handleCommand(RowCommand.WRITE);
    };
    const handleEditDetails = (): void => {
      this.handleCommand(RowCommand.EDIT);
    };

    const createButtonElement = (
      buttonKey,
      intent,
      hasErrors,
      hasUserAccess,
      icon,
      text,
      handler,
    ): ReactElement<Button> => {
      const isDisabled = !(hasUserAccess && !hasErrors);
      if (buttonKey === BUTTON_KEYS.DELETE) {
        return (
          <RecordDelete
            disabled={isDisabled}
            icon={icon}
            intent={intent}
            key={buttonKey}
            minimal
            onConfirm={handler}
            tabIndex={-1}
          />
        );
      } else {
        return (
          <Tooltip placement="bottom" title={text}>
            <Button
              disabled={isDisabled}
              icon={icon}
              intent={intent}
              key={buttonKey}
              minimal
              onClick={handler}
              tabIndex={-1}
            />
          </Tooltip>
        );
      }
    };

    const buttons = [];

    switch (rowState) {
      case RowState.PUBLISHED:
        if (equals(collectionGroupId, CollectionGroupId.calendar)) {
          buttons.push(
            createButtonElement(
              BUTTON_KEYS.STORY,
              Intent.PRIMARY,
              hasErrors,
              permissions.canPostStory,
              IconNames.EDIT,
              'Edit Row',
              handleStoryWrite,
            ),
          );
        } else {
          buttons.push(
            createButtonElement(
              BUTTON_KEYS.DELETE,
              Intent.DANGER,
              false,
              permissions.canDeleteRow,
              IconNames.DELETE,
              'Delete Row',
              handleRowDelete,
            ),
          );
        }
        break;
      case RowState.DRAFT:
        buttons.push(
          createButtonElement(
            BUTTON_KEYS.SAVE,
            Intent.SUCCESS,
            hasErrors,
            permissions.canPublishRow,
            IconNames.SMALL_TICK,
            'Publish Row',
            handleRowSave,
          ),
        );
        buttons.push(
          createButtonElement(
            BUTTON_KEYS.DELETE,
            Intent.DANGER,
            false,
            permissions.canDeleteRow,
            IconNames.DELETE,
            'Delete Row',
            handleRowDelete,
          ),
        );
        if (equals(collectionGroupId, CollectionGroupId.calendar)) {
          buttons.push(
            createButtonElement(
              BUTTON_KEYS.STORY,
              Intent.PRIMARY,
              hasErrors,
              permissions.canPostStory,
              IconNames.EDIT,
              'Edit Row',
              handleStoryWrite,
            ),
          );
        }
        break;
      case RowState.MODIFIED:
        buttons.push(
          createButtonElement(
            BUTTON_KEYS.SAVE,
            Intent.SUCCESS,
            hasErrors,
            permissions.canPublishRow,
            IconNames.SMALL_TICK,
            'Publish Row',
            handleRowSave,
          ),
        );
        buttons.push(
          createButtonElement(
            BUTTON_KEYS.DISCARD,
            Intent.DANGER,
            false,
            permissions.canDiscardRow,
            IconNames.SMALL_CROSS,
            'Discard Changes',
            handleRowDiscard,
          ),
        );
        if (equals(collectionGroupId, CollectionGroupId.calendar)) {
          buttons.push(
            createButtonElement(
              BUTTON_KEYS.STORY,
              Intent.PRIMARY,
              hasErrors,
              permissions.canPostStory,
              IconNames.EDIT,
              'Edit Row',
              handleStoryWrite,
            ),
          );
        }
        break;
      case RowState.REVISION:
        buttons.push(
          createButtonElement(
            BUTTON_KEYS.REVERT,
            Intent.WARNING,
            false,
            permissions.canRevertRow,
            IconNames.UNDO,
            'Revert Row',
            handleRowRevert,
          ),
        );
        break;
      default:
        break;
    }

    if (equals(data.collectionId, CollectionId.bullsBearsSay)) {
      const buttonContent = (
        <div>
          <Tooltip placement="bottom" title={'View Row'}>
            <Button
              icon={IconNames.EDIT}
              intent={Intent.PRIMARY}
              key={BUTTON_KEYS.STORY}
              minimal
              onClick={handleEditDetails}
              tabIndex={-1}
            />
          </Tooltip>
        </div>
      );

      buttons.push(buttonContent);
    }

    if (
      !isNull(updateHistory) &&
      !isUndefined(updateHistory) &&
      !isEmpty(updateHistory) &&
      !isNil(updateHistory) &&
      !equals(rowState, RowState.PUBLISHED)
    ) {
      const buttonContent = (
        <div>
          <Tooltip placement="bottom" title={'View Row'}>
            <Button
              icon={IconNames.EYE_OPEN}
              intent={Intent.PRIMARY}
              key={BUTTON_KEYS.UPDATE_HISTORY_VIEW}
              minimal
              onClick={this.handleViewClick}
              tabIndex={-1}
            />
          </Tooltip>
        </div>
      );

      buttons.push(buttonContent);
    }

    return <div className="grid-cell-button--wrapper">{buttons}</div>;
  };

  handleConfirmDialog = (): void => {
    const { data, onRowPublish } = this.props;

    const modifiedRow: Row = {
      data,
      errors: {},
      state: data.state,
    };
    this.setState({ showConfirmationDialog: false });
    onRowPublish(data.collectionId, modifiedRow);
  };

  onCancel = (): void => {
    this.setState({ showConfirmationDialog: false });
  };

  render() {
    let rowData = [];
    const { data } = this.props;
    const { showConfirmationDialog, showHistoryModal } = this.state;

    const { update_history: updateHistory } = data;

    const renderButtons = (
      <div className="bp5-dialog-footer">
        <div className="bp5-dialog-footer-actions">
          <Button intent={Intent.DANGER} onClick={this.handleConfirmDialog} text="Confirm" />
          <Button intent={Intent.NONE} onClick={this.onCancel} text="Cancel" />
        </div>
      </div>
    );

    const renderContent = (
      <div className="bp5-dialog-body">Either EPS or Revenue is empty! Are you sure you want to continue?</div>
    );

    const isStringArray = array => {
      const arrayElementsType = map(e => typeof e)(array);
      const typeToDetect = equals('string');
      const areAllString = all(typeToDetect);
      return areAllString(arrayElementsType);
    };

    const isObjectArray = array => {
      const arrayElementsType = map(e => typeof e)(array);
      const typeToDetect = equals('object');
      const areAllString = all(typeToDetect);
      return areAllString(arrayElementsType);
    };

    const buildValue = value => {
      if (isBoolean(value)) {
        return value ? 'Y' : 'N';
      } else if (isArray(value) && isStringArray(value)) {
        return join(', ')(value);
      } else if (isArray(value) && isObjectArray(value)) {
        // name for companies and symbol for tickers
        const data = map((x: any) => x.name || x.symbol)(value);
        return join(', ')(data);
      } else if (isObj(value) && !isEmpty(value) && !isNil(value)) {
        if (has('name')(value)) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore `name` is not present on {} | Function!!
          return value.name;
        } else if (has('full_name')(value)) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore `full_name` is not present on {} | Function!!
          return value.full_name;
        } else {
          return pipe(defaultTo([]), map(prop('name')), join(', '))(value);
        }
      } else {
        return value;
      }
    };

    if (showHistoryModal) {
      if (!isNull(updateHistory) && !isUndefined(updateHistory) && !isEmpty(updateHistory) && !isNil(updateHistory)) {
        rowData = mapIndexed(
          row => ({
            field: row.field,
            newValue: buildValue(row.new_value),
            oldValue: buildValue(row.previous_value),
            updatedBy: row.updated_by,
          }),
          updateHistory,
        );
      }

      const updateHistoryGrid = (
        <div className="ag-theme-balham grid-size">
          <AgGridReact columnDefs={history.definitions} gridOptions={this.gridOptions} rowData={rowData} />
        </div>
      );

      const modalContent = (
        <Overlay
          canEscapeKeyClose
          canOutsideClickClose
          className={`overlay-size align-center ${Classes.OVERLAY_CONTENT}`}
          isOpen={showHistoryModal}
          onClose={this.handleViewClick}
          usePortal
        >
          <div className="grid-container">{updateHistoryGrid}</div>
        </Overlay>
      );

      return (
        <div>
          {showHistoryModal && modalContent}
          <div className="calendar-cell align-center">{this.renderButtons(data)}</div>
        </div>
      );
    }

    return (
      <div className="calendar-cell align-center">
        {this.renderButtons(data)}
        <Dialog
          canEscapeKeyClose={false}
          canOutsideClickClose={false}
          className="bz-alert-dialog"
          icon="ban-circle"
          isCloseButtonShown={false}
          isOpen={showConfirmationDialog}
          onClose={this.onCancel}
          title="Confirmation"
        >
          {renderContent}
          {renderButtons}
        </Dialog>
      </div>
    );
  }
}

const mapDispatchToProps = (dispatch: Dispatch): DispatchableActions => ({
  onCreatePost: (row: Row) => dispatch(createCalendarPost(row)),
  onEditDetail: (row: Row) => dispatch(editCalendarDetails(row)),
  onRowDelete: (collectionId: CollectionId, row: Row) => dispatch(deleteRowAsync(collectionId, row)),
  onRowDiscard: (collectionId: CollectionId, row: Row) => dispatch(discardRowAsync(collectionId, row)),
  onRowPublish: (collectionId: CollectionId, row: Row) => dispatch(publishRowAsync(collectionId, row)),
  onRowRevert: (collectionId: CollectionId, rowId: string) => dispatch(revertRowAsync(collectionId, rowId)),
});

export default connect<null, DispatchableActions>(null, mapDispatchToProps)(CollectionActions);
