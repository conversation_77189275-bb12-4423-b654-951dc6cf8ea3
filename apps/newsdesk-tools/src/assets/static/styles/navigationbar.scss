.bz-theme {
  .bz-logo {
    margin: 5px;
    background-image: url("/assets/static/img/favicon-white-32x32.png");
    background-repeat: no-repeat;
    background-size: 100% auto;
    width: 32px;
    height: 32px;
  }

  .bp5-navbar {
    background-color: $primary-color;
    color: $secondary-color;
    overflow: hidden;

    a {
      color: inherit;
      text-decoration: none;
    }

    .bp5-button.bp5-minimal, .bp5-button .bp5-icon {
      color: inherit;
    }

    .bp5-button:not([class*="bp5-intent-"])[class*="bp5-icon-"]::before {
      color: $secondary-color;
    }

    .pt-spinner.bz-spinner {
      .pt-spinner-head {
        stroke: $secondary-color;
      }
      .pt-spinner-track {
        stroke: lightgray;
      }
    }

    .pt-link {
      padding: 10px;
    }
  }
}
