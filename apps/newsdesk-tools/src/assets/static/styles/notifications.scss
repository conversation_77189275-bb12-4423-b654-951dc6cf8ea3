.notifications,
.logos {
	display: flex;
	flex-direction: column;
	padding-bottom: 20px;
	.spinner-container {
		height: 100%;
		margin: 0px auto;
		position: absolute;
		top: 60%;
		width: 100%;
	}
	.notifications-container {
		max-width: fit-content;
		.form-field {
			margin-bottom: 20px;
			.input-field-wrap {
				display: flex;
			}
		}
		.image-container {
			height: 75px;
			width: 75px;
			img {
				height: 100%;
				width: 100%;
			}
		}
		.label {
			font-weight: 600;
			margin: 5px 0;
		}
		.datepicker-container {
			margin-top: 10px;
			.inner {
				display: flex;
				justify-content: space-between;
				.datepicker {
					display: inline-block;
					width: 270px;
					.label {
						font-weight: 600;
					}
					.border {
						border: 1px solid #cecece;
						border-radius: 3px;
					}
					.footer {
						text-align: center;
						margin-top: 5px;
					}
				}
			}
		}
		.button-container {
			display: flex;
			justify-content: center;
			margin: 20px 0;
			.bp5-button {
				margin: 0 5px;
			}
		}
		.error-message {
			text-align: center;
			margin-bottom: 10px;
			color: #db3737;
		}
	}
	.notifications-grid {
		display: flex;
		height: 50vh;
		width: 90%;
		flex-direction: column;
		.utility-button-container {
			.bp5-button {
				zoom: 75%;
			}
		}
	}
	.logos-grid {
		display: flex;
		height: 50vh;
		width: 100%;
		flex-direction: column;
		.logo-image-grid-preview {
			img {
				max-width:100%;
				max-height:50px;
				padding: 5px;
				object-fit: contain;
			}
			span {
				height: 50px;
			}
		}
		.utility-button-container {
			.bp5-button {
				zoom: 75%;
			}
		}
		.logo-color-swatch {
			p {
				display: inline;
				margin-right: 10px;
			}
			svg {
				width: 30px;
				padding-top: 10px;
				padding-left: 5px;
				stroke: black;
				stroke-width: 1px;
			}
		}
	}

	.logos-container {
		padding: 30px;
		background-color: #e3e3e321;
		border-radius: 10px;
		.form-field {
			width: 40%;
			margin-bottom: 20px;
			.input-field-wrap {
				display: flex;
			}
		}
		.image-container {
			margin-top: 5px;
			height: 75px;
			width: 75px;
			img {
				height: 100%;
				width: 100%;
				border: 1px solid #ddd;
				padding: 5px;
				border-radius: 4px;
				object-fit: contain;
			}
		}
		.label {
			font-weight: 600;
			margin: 5px 0;
		}

		.error-message {
			text-align: center;
			margin-bottom: 10px;
			color: #db3737;
		}
		.bp5-file-input {
			width: 100%;
		}
		.bp5-callout.bp5-intent-danger {
			h3 {
				margin: 0px;
			}
		}
		.errors-ul {
			margin: 0px;
			padding-inline-start: 16px;
		}
		.error-detail{
			text-transform:capitalize;
		}
	}
}
