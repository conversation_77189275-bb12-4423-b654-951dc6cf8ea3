.squawk {
  display: flex;
  flex-direction: column;
  padding-left: 100px;
  padding-right: 100px;
  width: 100%;

  &__header {
    font-size: 36px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    font-weight: 400;
  }

  &__broadcast-btn {
    padding: 6px 30px 6px 60px;
    width: 330px;
    line-height: 24px;
    cursor: pointer;
    border: 1px solid #666;
    border-radius: 2px;
    outline: none;
    font-size: 24px;
    color: #fff;
    background-color: #5cb85c;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 40px;
    &:disabled {
      background: #f5bc42;
      cursor: not-allowed;
    }
  }

  &__channel-dropdown {
    padding: 6px 0px 22px 0px;
    width: 330px;
    line-height: 24px;
    cursor: pointer;
  }

  $spacing: 16px;

  &__volume {
    width: 400px;
    display: flex;
    padding: 0 $spacing;
    align-items: center;
    border: 1px solid #dee2e6;
    border-radius: 2px;

    &-control {
      margin: 0 $spacing;
      transform: translateY(31%);
      .bp5-slider-handle {
        border-radius: 50%;
        outline: none;
      }
      .bp5-slider-label {
        display: none;
      }
    }

    .bp5-icon {
      color: #484e56;
    }

    &-value {
      margin-left: calc($spacing / 2);
      color: #484e56;
      width: 32px;
      display: inline-block;
      text-align: right;
    }
  }

  &__title {
    font-size: $spacing;
    font-weight: 500;
    margin-bottom: 8px;
  }

  &__broadcasters-list {
    margin-bottom: $spacing;
  }

  &__audio-visualizer {
    border: 1px solid #ccc;
    border-radius: 3px;
    width: 300px;
    height: 150px;
    margin-bottom: $spacing;
    margin-top: $spacing;
  }

  &__mute-toggle {
    margin-left: $spacing;
    margin-bottom: 5px;
  }

  &__console {
    margin-top: 20px;
    &-header {
      display: inline-block;
      max-width: 100%;
      margin-bottom: 5px;
      font-weight: 700;
    }
  }

  &__log {
    &-container {
      display: block;
      $fontSize: 14px;
      font-size: $fontSize;
      line-height: calc(20px / $fontSize);
      color: #555;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
      transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
      overflow-y: auto;
      width: 100%;
      height: 100%;
      max-height: 400px;
      margin: 0;
      padding: 6px;
      list-style-type: none;
    }

    &-message {
      $spacing: 14px;
      padding-left: $spacing;
      margin-bottom: 5px;
      display: flex;
      white-space: pre;
      &:before {
        color: #888;
        content: '>';
        font-weight: bold;
        margin-left: -$spacing;
      }

      &--info {
        color: green;
      }
      &--error {
        color: red;
      }
    }
  }


}
