import { DateTime } from 'luxon';
import { homePageWorkspace } from '../components/dashboard/homeMenu/homeMenu';
import { Workspace, WorkspaceId, Workspaces } from '../components/dashboard/workspaceEntity';
import { WorkspacesById } from '../entities/layoutEntity';
import { generateWorkspaceId } from '../utils/entities';

export const INITIAL_WORKSPACE_ID = generateWorkspaceId();

export const INITIAL_WORKSPACE_STATE = {
  activeWorkspaceId: INITIAL_WORKSPACE_ID,
  hiddenWorkspaceIds: [],
  workspaceIds: [INITIAL_WORKSPACE_ID],
};

const WORKSPACE_PREFIX = 'Workspace ';

export const getWorkspaceName = (workspaces: Workspace[]) => {
  const indices = workspaces.reduce(
    (acc, { name }) => (name.startsWith(WORKSPACE_PREFIX) ? [...acc, name.replace(WORKSPACE_PREFIX, '')] : acc),
    [] as string[],
  );
  let i = 1;
  while (indices.includes(`${i}`)) {
    i++;
  }
  return `${WORKSPACE_PREFIX}${i}`;
};

export function createWorkspace(workspaceId: WorkspaceId, params?: Partial<Workspace>): Workspace {
  return {
    config: null,
    lastAccessed: DateTime.local().toMillis(),
    name: '',
    version: 3,
    workspaceId,
    ...params,
  };
}

export const INITIAL_WORKSPACE_LIST: WorkspacesById<Workspace> = {
  [INITIAL_WORKSPACE_ID]: createWorkspace(INITIAL_WORKSPACE_ID),
};

export const getInitialWorkspaceState = (): Workspaces => ({
  activeWorkspaceId: 'welcome-page',
  'home-page': homePageWorkspace,
  'morning-report': { isOnPlatformBar: false, lastAccessed: null },
  userWorkspaces: [],
  'welcome-page': { currentPanel: { panel: 'welcome' }, isOnPlatformBar: true, lastAccessed: null },
});
