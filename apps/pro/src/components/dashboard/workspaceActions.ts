import { ThunkDispatch } from 'redux-thunk';
import { RootState, RootAction, Thunk } from '../../redux/types';

////////////////////////////////////////////
///////      importing entities      ///////
////////////////////////////////////////////

import { WidgetId } from '@benzinga/widget-tools';
import { PreDefinedWorkspaces, WelcomePagePanel, WorkspaceId } from './workspaceEntity';
import { GenericWidget } from '@benzinga/widget-tools';

import { addWidget } from '../../actions/widgetActions';

/////////////////////////////////////////////
///////      importing selectors      ///////
/////////////////////////////////////////////

import {
  selectActiveWorkspace,
  selectWorkspaceById,
  selectUserWorkspaces,
  selectWorkspaceWidgetIds,
} from './workspaceSelectors';
import { generateWorkspaceId } from '../../utils/entities';
import { MosaicNode } from 'react-mosaic-component';
import { WidgetPreset } from './presets/entities';
import { TrackingManager } from '@benzinga/tracking-manager';
import { selectSession } from '../../selectors/sessionSelectors';

//////////////////////////////////////////
///////      define constants      ///////
//////////////////////////////////////////

export const CHANGE_WORKSPACE_NAME = 'CHANGE_WORKSPACE_NAME';
export const UPDATE_WORKSPACE = 'UPDATE_WORKSPACE';
export const REMOVE_WORKSPACE = 'REMOVE_WORKSPACE';
export const HIDE_WORKSPACE = 'HIDE_WORKSPACE';
export const RESTORE_WORKSPACE = 'RESTORE_WORKSPACE';
export const ADD_WORKSPACE = 'ADD_WORKSPACE';
export const OPEN_WELCOME_PAGE = 'OPEN_WELCOME_PAGE';
export const OPEN_MORNING_REPORT_PAGE = 'OPEN_MORNING_REPORT_PAGE';
export const MOVE_WORKSPACE_TAB = 'MOVE_WORKSPACE_TAB';
export const SWITCH_WORKSPACE = 'SWITCH_WORKSPACE';

//////////////////////////////////////////
///////      switch workspace      ///////
//////////////////////////////////////////

export interface SwitchWorkspaceAction {
  payload: {
    workspaceId: WorkspaceId;
  };
  type: typeof SWITCH_WORKSPACE;
}

export const switchWorkspace =
  (workspaceId: WorkspaceId | 'home-page'): Thunk<void> =>
  (dispatch, getState) => {
    if (workspaceId === 'home-page') {
      selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('view', {
        workspace_id: 'home-page',
      });
    }
    const activeWorkspaceId = selectActiveWorkspace(getState()).workspaceId;
    if (activeWorkspaceId !== workspaceId) {
      const switchWorkspaceAction: SwitchWorkspaceAction = {
        payload: {
          workspaceId,
        },
        type: SWITCH_WORKSPACE,
      };
      dispatch(switchWorkspaceAction);
    }
  };

////////////////////////////////////////
///////      move workspace      ///////
////////////////////////////////////////

export interface MoveWorkspaceTabAction {
  payload: {
    newIndex: number;
  };
  type: typeof MOVE_WORKSPACE_TAB;
}

export const moveWorkspaceTab = (newIndex: number): MoveWorkspaceTabAction => ({
  payload: {
    newIndex,
  },
  type: MOVE_WORKSPACE_TAB,
});

///////////////////////////////////////
///////      add workspace      ///////
///////////////////////////////////////

export interface AddWorkspaceAction {
  payload: {
    workspaceId: WorkspaceId;
  };
  type: typeof ADD_WORKSPACE;
}

export const addWorkspace =
  (name?: string, config?: MosaicNode<number> | null, selectedWidgets?: WidgetPreset[]): Thunk<AddWorkspaceAction> =>
  (dispatch, getState) => {
    const workspaceId = generateWorkspaceId();
    const action: AddWorkspaceAction = {
      payload: {
        workspaceId,
      },
      type: ADD_WORKSPACE,
    };
    dispatch(action);
    const workspaces = selectUserWorkspaces(getState());
    if (name) {
      dispatch(changeWorkspaceName(workspaceId, name));
    }

    if (config) {
      selectedWidgets && dispatch(updateWorkspaceWithWidgets(workspaceId, config, selectedWidgets));
    } else {
      selectedWidgets?.map(w => dispatch(addWidget(w.widgetType, w.config ?? undefined, 'current')));
    }
    selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('add_workspace', {
      workspace_count: workspaces.length,
    });
    return action;
  };

export interface OpenWelcomePageAction {
  type: typeof OPEN_WELCOME_PAGE;
  panel: WelcomePagePanel;
}

export const openWelcomePage =
  (panel?: WelcomePagePanel): Thunk<OpenWelcomePageAction> =>
  dispatch => {
    const action: OpenWelcomePageAction = {
      panel: panel ?? { panel: 'welcome' },
      type: OPEN_WELCOME_PAGE,
    };
    dispatch(action);
    return action;
  };

export interface OpenMorningReportAction {
  type: typeof OPEN_MORNING_REPORT_PAGE;
}

export const openMorningReportPage =
  (): Thunk<OpenMorningReportAction | SwitchWorkspaceAction> => (dispatch, getState) => {
    if (
      selectWorkspaceById(getState(), { workspaceId: PreDefinedWorkspaces.MorningReport })?.workspaceId ===
      PreDefinedWorkspaces.MorningReport
    ) {
      const switchWorkspaceAction: SwitchWorkspaceAction = {
        payload: {
          workspaceId: PreDefinedWorkspaces.MorningReport,
        },
        type: SWITCH_WORKSPACE,
      };
      dispatch(switchWorkspaceAction);
      return switchWorkspaceAction;
    } else {
      const action: OpenMorningReportAction = {
        type: OPEN_MORNING_REPORT_PAGE,
      };
      dispatch(action);
      return action;
    }
  };

///////////////////////////////////////
///////  add default workspace  ///////
///////////////////////////////////////

const getDefaultWorkspaces = () => {
  const tabsById = {
    calendar: {
      label: 'Calendar',
      subTabs: [
        'calendar:conference',
        'calendar:dividends',
        'calendar:earnings',
        'calendar:guidance',
        'calendar:ipos',
        'calendar:ma',
        'calendar:offerings',
        'calendar:ratings',
        'calendar:sec',
        'calendar:splits',
      ],
      tabId: 'calendar',
    },
    'calendar:conference': {
      label: 'Conference Calls',
      subTabs: [],
      tabId: 'calendar:conference',
    },
    'calendar:dividends': {
      label: 'Dividends',
      subTabs: [],
      tabId: 'calendar:dividends',
    },
    'calendar:earnings': {
      label: 'Earnings',
      subTabs: [],
      tabId: 'calendar:earnings',
    },
    'calendar:guidance': {
      label: 'Guidance',
      subTabs: [],
      tabId: 'calendar:guidance',
    },
    'calendar:ipos': {
      label: 'IPOs',
      subTabs: [],
      tabId: 'calendar:ipos',
    },
    'calendar:ma': {
      label: 'Mergers & Acquisitions',
      subTabs: [],
      tabId: 'calendar:ma',
    },
    'calendar:offerings': {
      label: 'Secondary Offerings',
      subTabs: [],
      tabId: 'calendar:offerings',
    },
    'calendar:ratings': {
      label: 'Analyst Ratings',
      subTabs: [],
      tabId: 'calendar:ratings',
    },
    'calendar:sec': {
      label: 'SEC Filings',
      subTabs: [],
      tabId: 'calendar:sec',
    },
    'calendar:splits': {
      label: 'Splits',
      subTabs: [],
      tabId: 'calendar:splits',
    },
    chart: {
      label: 'Chart',
      subTabs: [],
      tabId: 'chart',
    },
    financials: {
      label: 'Financials',
      subTabs: ['financials:balance', 'financials:income', 'financials:cash'],
      tabId: 'financials',
    },
    'financials:balance': {
      label: 'Balance Sheet',
      subTabs: [],
      tabId: 'financials:balance',
    },
    'financials:cash': {
      label: 'Cash Flow',
      subTabs: [],
      tabId: 'financials:cash',
    },
    'financials:income': {
      label: 'Income Statement',
      subTabs: [],
      tabId: 'financials:income',
    },
    newsfeed: {
      label: 'Newsfeed',
      subTabs: [
        'newsfeed:all',
        'newsfeed:sec',
        'newsfeed:press',
        'newsfeed:bzWire',
        'newsfeed:bzSignal',
        'newsfeed:partner',
      ],
      tabId: 'newsfeed',
    },
    'newsfeed:all': {
      label: 'All',
      subTabs: [],
      tabId: 'newsfeed:all',
    },
    'newsfeed:bzSignal': {
      label: 'BZ Signals',
      subTabs: [],
      tabId: 'newsfeed:bzSignal',
    },
    'newsfeed:bzWire': {
      label: 'BZ Wire',
      subTabs: [],
      tabId: 'newsfeed:bzWire',
    },
    'newsfeed:partner': {
      label: 'Partner News',
      subTabs: [],
      tabId: 'newsfeed:partner',
    },
    'newsfeed:press': {
      label: 'Press Releases',
      subTabs: [],
      tabId: 'newsfeed:press',
    },
    'newsfeed:sec': {
      label: 'SEC Filings',
      subTabs: [],
      tabId: 'newsfeed:sec',
    },
    overview: {
      label: 'Overview',
      subTabs: [],
      tabId: 'overview',
    },
  };
  const defaultWorkspaces: {
    title: string;
    config: MosaicNode<number> | null;
    widgets: Omit<GenericWidget, 'widgetId'>[];
  }[] = [];

  return defaultWorkspaces;
};

const updateConfigId = (
  config: MosaicNode<number> | MosaicNode<WidgetId> | null,
  prev: number,
  next: WidgetId,
): MosaicNode<number> | MosaicNode<WidgetId> | null => {
  if (config === null) {
    return config;
  } else if (typeof config === 'object') {
    return {
      ...config,
      first: updateConfigId(config.first, prev, next) as MosaicNode<number>,
      second: updateConfigId(config.second, prev, next) as MosaicNode<number>,
    };
  } else {
    if (config === prev) {
      return next;
    } else {
      return config;
    }
  }
};

//////////////////////////////////////////
///////      change workspace      ///////
//////////////////////////////////////////

// ChangeWorkspaceNameAction action type
export interface ChangeWorkspaceNameAction {
  payload: {
    name: string;
    workspaceId: WorkspaceId;
  };
  type: typeof CHANGE_WORKSPACE_NAME;
}

export const changeWorkspaceName =
  (workspaceId: WorkspaceId, name: string): Thunk<ChangeWorkspaceNameAction> =>
  (dispatch, getState) => {
    const action: ChangeWorkspaceNameAction = {
      payload: {
        name,
        workspaceId,
      },
      type: CHANGE_WORKSPACE_NAME,
    };
    dispatch(action);
    selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('update_layout', {
      property_change: 'name',
      workspace_id: workspaceId,
    });
    return action;
  };

///////////////////////////////////////
///////      set pane size      ///////
///////////////////////////////////////

export interface UpdateWorkspaceAction {
  payload: {
    config: MosaicNode<WidgetId> | null;
    workspaceId: WorkspaceId;
  };
  type: typeof UPDATE_WORKSPACE;
}

export const updateWorkspace =
  (workspaceId: WorkspaceId, config: MosaicNode<WidgetId> | null): Thunk<void> =>
  (dispatch, getState) => {
    selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('update_layout', {
      workspace_id: workspaceId,
    });
    const workspace = selectWorkspaceById(getState(), { workspaceId });
    if (workspace) {
      dispatch({
        payload: {
          config,
          workspaceId,
        },
        type: UPDATE_WORKSPACE,
      });
    }
  };

export const updateWorkspaceWithWidgets =
  (workspaceId: WorkspaceId, config: MosaicNode<number> | null, widgets: WidgetPreset[]): Thunk<void> =>
  async dispatch => {
    if (config) {
      const widgetsAction = await Promise.all(
        widgets.map(w => dispatch(addWidget(w.widgetType, w.config ?? undefined, 'none'))),
      );

      const newConfig = widgetsAction.reduce<MosaicNode<WidgetId> | null>(
        (acc, w, index) =>
          w
            ? (updateConfigId(acc, index, w as unknown as string) as MosaicNode<WidgetId> | null)
            : (updateConfigId(acc, index, '') as MosaicNode<WidgetId> | null),
        config as any,
      );

      dispatch(updateWorkspace(workspaceId, newConfig));
    } else {
      widgets.forEach(w => dispatch(addWidget(w.widgetType, w.config ?? undefined, workspaceId)));
    }
  };

////////////////////////////////////////
///////      hide workspace      ///////
////////////////////////////////////////

export interface HideWorkspaceAction {
  payload: {
    removeWorkspace: RemoveWorkspaceAction;
    restoreWorkspace: () => void;
    widgetIds: string[];
    workspaceId: WorkspaceId;
    workspaceName: string;
  };
  type: typeof HIDE_WORKSPACE;
}

export const hideWorkspace = (
  workspaceName: string,
  widgetIds: string[],
  workspaceId: string,
  dispatch: ThunkDispatch<RootState, void, RootAction>,
): HideWorkspaceAction => {
  const restoreAction: RestoreWorkspaceAction = {
    payload: {
      workspaceId,
    },
    type: RESTORE_WORKSPACE,
  };

  const removeWorkspace: RemoveWorkspaceAction = {
    payload: {
      widgetIds,
      workspaceId,
    },
    type: REMOVE_WORKSPACE,
  };

  const restoreWorkspace = () => {
    dispatch(restoreAction);
    const switchWorkspaceAction: SwitchWorkspaceAction = {
      payload: {
        workspaceId,
      },
      type: SWITCH_WORKSPACE,
    };
    dispatch(switchWorkspaceAction);
  };

  return {
    payload: {
      removeWorkspace,
      restoreWorkspace,
      widgetIds,
      workspaceId,
      workspaceName,
    },
    type: HIDE_WORKSPACE,
  };
};

////////////////////////////////////////////
///////      restore workspace      ////////
////////////////////////////////////////////

export interface RestoreWorkspaceAction {
  payload: {
    workspaceId: WorkspaceId;
  };
  type: typeof RESTORE_WORKSPACE;
}

export const restoreWorkspace = (workspaceId: WorkspaceId): RestoreWorkspaceAction => {
  return {
    payload: {
      workspaceId,
    },
    type: RESTORE_WORKSPACE,
  };
};

///////////////////////////////////////////
///////      remove workspace      ///////
///////////////////////////////////////////

export interface RemoveWorkspaceAction {
  payload: {
    widgetIds: WidgetId[];
    workspaceId: WorkspaceId;
  };
  type: typeof REMOVE_WORKSPACE;
}

export const removeWorkspace =
  (workspaceId: WorkspaceId): Thunk<void> =>
  (dispatch, getState) => {
    const state = getState();
    const widgetIds = selectWorkspaceWidgetIds(state, { workspaceId });
    const workspaces = selectUserWorkspaces(state);
    const activeWorkspaceId = selectActiveWorkspace(getState()).workspaceId;
    const isActiveWorkspace = workspaceId === activeWorkspaceId;

    if (isActiveWorkspace) {
      const sortedWorkspaces = workspaces.sort((a, b) => (a.lastAccessed ?? 0) - (b.lastAccessed ?? 0));
      const newActiveWorkspaceId =
        sortedWorkspaces[0]?.workspaceId === workspaceId
          ? sortedWorkspaces[1]?.workspaceId
          : sortedWorkspaces[0]?.workspaceId;
      const switchWorkspaceAction: SwitchWorkspaceAction = {
        payload: {
          workspaceId: newActiveWorkspaceId ?? 'home-page',
        },
        type: SWITCH_WORKSPACE,
      };
      dispatch(switchWorkspaceAction);
    }

    const removeWorkspaceAction: RemoveWorkspaceAction = {
      payload: {
        widgetIds,
        workspaceId,
      },
      type: REMOVE_WORKSPACE,
    };
    dispatch(removeWorkspaceAction);

    selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('delete_layout', {
      workspace_count: workspaces.length,
      workspace_id: workspaceId,
    });
  };

///////////////////////////////////////////////////////
///////      export workspsce action types      ///////
///////////////////////////////////////////////////////

export type WorkspaceActions =
  | AddWorkspaceAction
  | OpenWelcomePageAction
  | OpenMorningReportAction
  | ChangeWorkspaceNameAction
  | HideWorkspaceAction
  | MoveWorkspaceTabAction
  | RemoveWorkspaceAction
  | RestoreWorkspaceAction
  | SwitchWorkspaceAction
  | UpdateWorkspaceAction;
