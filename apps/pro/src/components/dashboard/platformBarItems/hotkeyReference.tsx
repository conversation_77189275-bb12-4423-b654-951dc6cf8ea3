import React from 'react';

import { MenuItem } from '../MenuItem';
import { Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import styled from '@benzinga/themetron';
import { faKeyboard } from '@fortawesome/pro-light-svg-icons';
import { SafeFontAwesomeIcon } from '../../common/SafeFontAwesomeIcon';

export const HotkeyReferenceIcon: React.FC<{
  menuHoverable: boolean;
}> = props => {
  const navigate = useNavigate();

  const onMenuClick = React.useCallback(() => {
    navigate('/dashboard/preferences/hotkeys/');
  }, [navigate]);
  return (
    <MenuItem data-button-hint="HotkeyReference" menuHoverable={props.menuHoverable} onClick={onMenuClick}>
      <Tooltip placement="bottom" title={'Show Hotkey Reference'}>
        <IconWrapper>
          <SafeFontAwesomeIcon icon={faKeyboard} />
        </IconWrapper>
      </Tooltip>
    </MenuItem>
  );
};

const IconWrapper = styled.div`
  svg path {
    fill: ${props => props.theme.colors.foreground};
  }
  display: flex;
  justify-content: center;
  align-items: center;
`;
