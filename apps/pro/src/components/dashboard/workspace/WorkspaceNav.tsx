import React, { ReactElement } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { ThunkDispatch } from 'redux-thunk';

import Dropdown from 'antd/lib/dropdown';
import styled, { css } from '@benzinga/themetron';

import { addWorkspace } from '../workspaceActions';
import { PreDefinedWorkspaces, Workspace, Workspaces } from '../../../components/dashboard/workspaceEntity';
import { RootAction, RootState } from '../../../redux/types';
import { selectActiveWorkspace, selectUserWorkspaces, selectWorkspaces } from '../workspaceSelectors';
import { isWindowWidthLargerThenSmall } from '../../../selectors/uiSelectors';
import { switchWorkspace } from '../workspaceActions';
import { selectActiveWorkspaceId } from '../workspaceSelectors';
import { WorkspaceId } from '../workspaceEntity';
import WorkspaceTab from './WorkspaceTab';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import { WidgetsById } from '../../../entities/widgetEntity';
import { selectWidgets } from '../../../selectors/widgetSelectors';

import { Added, Down, Handwave, Home, Sun } from '@benzinga/valentyn-icons';
import Hooks from '@benzinga/hooks';
import { ProContext } from '@benzinga/pro-tools';
import { DeletionList } from './DeletionList';

interface OwnProps {
  className: string;
  platformBar: HTMLDivElement | null;
}

interface ReduxState {
  activeWorkspace: Workspace;
  isWindowWidthLargerThenSmall: boolean;
  selectedWorkspaceId: WorkspaceId;
  widgetsById: WidgetsById;
  userWorkspaces: Workspace[];
  workspaces: Workspaces;
}

interface DispatchableActions {
  switchWorkspace: typeof switchWorkspace;
  addWorkspace: typeof addWorkspace;
}

type Props = OwnProps & ReduxState & DispatchableActions;

interface State {
  hasScrolling: boolean;
  isTabsDropdownVisible: boolean;
  scrollInterval: NodeJS.Timeout | null;
}

const WorkspaceNav: React.FC<Props> = (props: Props) => {
  const [state, setState] = React.useState<State>({
    hasScrolling: false,
    isTabsDropdownVisible: false,
    scrollInterval: null,
  });
  const proContext = React.useContext(ProContext);
  const mousePositionX = React.useRef(0);
  const WORKSPACE_NAV = React.useRef<HTMLDivElement>(null);
  const userWorkspaces = React.useMemo(() => {
    const getWorkspacesToBeDeleted = proContext.getWorkspacesToBeDeleted;
    return props.userWorkspaces.filter(w => !getWorkspacesToBeDeleted().includes(w.workspaceId));
  }, [proContext.getWorkspacesToBeDeleted, props.userWorkspaces]);

  const checkCanScroll = React.useCallback(() => {
    if (WORKSPACE_NAV.current) {
      if (WORKSPACE_NAV.current.scrollWidth > WORKSPACE_NAV.current.clientWidth) {
        setState(s => ({ ...s, hasScrolling: true }));
        const elements = WORKSPACE_NAV.current.getElementsByClassName('is-active');
        if (elements.length > 0) {
          elements[0].scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'center' });
        }
      } else {
        setState(s => ({ ...s, hasScrolling: false }));
      }
    }
  }, []);
  const handleDropdownVisibilityChange = React.useCallback((isTabsDropdownVisible: boolean) => {
    setState(s => ({ ...s, isTabsDropdownVisible }));
  }, []);

  const handleTabClick = React.useCallback(() => {
    setState(s => ({ ...s, isTabsDropdownVisible: !s.isTabsDropdownVisible }));
  }, []);

  const renderDropdownMenu = React.useCallback(
    (items: ReactElement<typeof WorkspaceTab>[]) => <ul className="WorkspaceNav-dropdown">{items}</ul>,
    [],
  );

  const getDropdownMenuContainer = React.useCallback(
    (triggerNode: HTMLElement): HTMLElement => props.platformBar || triggerNode,
    [props.platformBar],
  );

  const onHomeOpenClick = React.useCallback(() => {
    const switchWorkspace = props.switchWorkspace;
    switchWorkspace('home-page');
  }, [props.switchWorkspace]);

  const handleWheel = React.useCallback((e: any) => {
    if (WORKSPACE_NAV.current) {
      if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
        if (e.deltaY > 0) {
          WORKSPACE_NAV.current.scrollLeft += 40;
        } else if (e.deltaY < 0) {
          WORKSPACE_NAV.current.scrollLeft -= 40;
        }
      }
    }
  }, []);

  const scrollWorspaceNav = React.useCallback(() => {
    let offsetSpeed = 0;
    if (mousePositionX.current <= 5) {
      offsetSpeed = (-5 + mousePositionX.current) * 4;
    } else if (mousePositionX.current >= 93.5) {
      offsetSpeed = (mousePositionX.current - 93.5) * 4;
    }
    WORKSPACE_NAV.current?.scrollTo(WORKSPACE_NAV.current?.scrollLeft + offsetSpeed / 3, 0);
  }, []);

  const mouseOver = React.useCallback(() => {
    if (state.scrollInterval) {
      clearInterval(state.scrollInterval);
    }
    setState(s => ({ ...s, scrollInterval: setInterval(scrollWorspaceNav, 15) }));
  }, [scrollWorspaceNav, state.scrollInterval]);

  const mouseOut = React.useCallback(() => {
    if (state.scrollInterval) {
      clearInterval(state.scrollInterval);
    }
    setState(s => ({ ...s, scrollInterval: null }));
  }, [state.scrollInterval]);

  const mouseMove = React.useCallback<React.MouseEventHandler<HTMLDivElement>>(e => {
    const rect = WORKSPACE_NAV.current?.getBoundingClientRect();

    if (rect) {
      mousePositionX.current = ((e.clientX - rect.left) / rect.width) * 100;
    }
  }, []);

  const addWorkspace = React.useCallback(() => {
    const addWorkspace = props.addWorkspace;
    addWorkspace();
  }, [props.addWorkspace]);

  React.useEffect(() => {
    window.addEventListener('resize', checkCanScroll);
    setTimeout(checkCanScroll, 100);
    return () => {
      window.removeEventListener('resize', checkCanScroll);
    };
  }, [checkCanScroll]);

  const prevWorkspacesLen = Hooks.usePrevious(userWorkspaces.length);
  const prevActiveWorkspaceId = Hooks.usePrevious(props.activeWorkspace.workspaceId);
  React.useEffect(() => {
    if (prevWorkspacesLen !== userWorkspaces.length) {
      checkCanScroll();
    }
    if (prevActiveWorkspaceId !== props.activeWorkspace.workspaceId) {
      checkCanScroll();
    }
  }, [
    checkCanScroll,
    prevActiveWorkspaceId,
    prevWorkspacesLen,
    props.activeWorkspace.workspaceId,
    userWorkspaces.length,
  ]);

  const getWorkspaceIcon = React.useCallback((workspace: Workspace) => {
    if (workspace.workspaceId === PreDefinedWorkspaces.MorningReport) {
      return (
        <IconWrapper>
          <Sun />
        </IconWrapper>
      );
    }

    return undefined;
  }, []);

  const workspaceList = React.useMemo(() => {
    let workspaceList = props.isWindowWidthLargerThenSmall
      ? userWorkspaces.flatMap((workspace: Workspace, index: number) => [
          <Separator key={workspace.workspaceId + 'split'}>
            <Mark
              dontDisplayMarker={
                (index > 0 && userWorkspaces[index - 1].workspaceId === props.activeWorkspace.workspaceId) ||
                workspace.workspaceId === props.activeWorkspace.workspaceId
              }
            />
          </Separator>,
          <WorkspaceTab
            active={workspace.workspaceId === props.activeWorkspace.workspaceId}
            editable={workspace.isEditable !== false}
            icon={getWorkspaceIcon(workspace)}
            index={index + (props.workspaces['welcome-page'].isOnPlatformBar ? 1 : 0)}
            key={workspace.workspaceId}
            onTabClick={handleTabClick}
            removeWorkspace={proContext.scheduleWorkspaceDelete}
            workspace={workspace}
          />,
        ])
      : [];

    if (
      props.workspaces['welcome-page'].isOnPlatformBar &&
      !proContext.getWorkspacesToBeDeleted().includes('welcome-page')
    ) {
      workspaceList = [
        <Separator key={'welcome-page split'}>
          <Mark />
        </Separator>,
        <WorkspaceTab
          active={'welcome-page' === props.activeWorkspace.workspaceId}
          editable={false}
          icon={
            <IconWrapper>
              <Handwave />
            </IconWrapper>
          }
          index={0}
          isDraggable={false}
          key={'welcome-page'}
          onTabClick={handleTabClick}
          removeWorkspace={proContext.scheduleWorkspaceDelete}
          workspace={{
            config: null,
            lastAccessed: 0,
            name: 'Welcome Page',
            version: 3,
            workspaceId: 'welcome-page',
          }}
        />,
        ...workspaceList,
      ];
    }

    return workspaceList;
  }, [
    getWorkspaceIcon,
    handleTabClick,
    proContext,
    props.activeWorkspace.workspaceId,
    props.isWindowWidthLargerThenSmall,
    userWorkspaces,
    props.workspaces,
  ]);

  const deletionListLength = React.useMemo(() => {
    const getWorkspacesToBeDeleted = proContext.getWorkspacesToBeDeleted;
    return getWorkspacesToBeDeleted().length;
  }, [proContext.getWorkspacesToBeDeleted]);

  return (
    <>
      {props.isWindowWidthLargerThenSmall && (
        <>
          <Separator>
            <Mark />
          </Separator>
          <MenuItemDiv
            className={`WorkspaceTab-homeWorkspace ${state.hasScrolling ? 'TabShadow-right' : ''}`}
            isSelected={props.selectedWorkspaceId === 'home-page'}
            onClick={onHomeOpenClick}
          >
            <IconWrapper>
              <Home height="1.25em" width="1.25em" />
            </IconWrapper>
            {props.selectedWorkspaceId === 'home-page' ? (
              <div className="WorkspaceTab-highlight" />
            ) : (
              <div className="WorkspaceTab-highlightInactive" />
            )}
          </MenuItemDiv>
        </>
      )}
      <WorkspacesHolder
        className={`${props.className} WorkspaceNav u-flexHorizontal`}
        onMouseEnter={mouseOver}
        onMouseLeave={mouseOut}
        onMouseMove={mouseMove}
        onWheel={handleWheel}
        ref={WORKSPACE_NAV}
      >
        <DndProvider backend={HTML5Backend}>{workspaceList}</DndProvider>
        <Separator key={'last split'}>
          <Mark />
        </Separator>
        {!state.hasScrolling && (
          <>
            <MenuItemDiv
              className="TUTORIAL_WorkspaceNav-Add WorkspaceTab-addWorkspace"
              isSelected={false}
              onClick={addWorkspace}
            >
              <IconWrapper>
                <Added title="New Workspace" />
              </IconWrapper>
            </MenuItemDiv>
            {workspaceList.length > 0 && (
              <DndProvider backend={HTML5Backend}>
                <Dropdown
                  dropdownRender={() => renderDropdownMenu(workspaceList)}
                  getPopupContainer={getDropdownMenuContainer}
                  onOpenChange={handleDropdownVisibilityChange}
                  trigger={['click']}
                >
                  <MenuItemDiv className="WorkspaceTab-dropdown" isSelected={false}>
                    <SVGWrapper shouldRotate={state.isTabsDropdownVisible}>
                      <IconWrapper>
                        <Down />
                      </IconWrapper>
                    </SVGWrapper>
                  </MenuItemDiv>
                </Dropdown>
              </DndProvider>
            )}
            {deletionListLength > 0 && (
              <MenuItemDiv
                className="TUTORIAL_WorkspaceNav-Add WorkspaceTab-addWorkspace TabShadow-left"
                isRed={true}
                isSelected={false}
              >
                <DeletionList
                  cancelDeletion={proContext.cancelWorkspacesDelete}
                  deletionCount={deletionListLength}
                  pauseDeletion={proContext.pauseWorkspacesDelete}
                  resumeDeletion={proContext.resumeWorkspacesDelete}
                />
              </MenuItemDiv>
            )}
          </>
        )}
        <div className="WorkspaceNav-whitespace"></div>
      </WorkspacesHolder>
      {props.isWindowWidthLargerThenSmall && state.hasScrolling && (
        <>
          <MenuItemDiv
            className="TUTORIAL_WorkspaceNav-Add WorkspaceTab-addWorkspace TabShadow-left"
            isSelected={false}
            onClick={addWorkspace}
          >
            <IconWrapper>
              <Added title="New Workspace" />
            </IconWrapper>
          </MenuItemDiv>
          {workspaceList.length > 0 && (
            <DndProvider backend={HTML5Backend}>
              <Dropdown
                getPopupContainer={getDropdownMenuContainer}
                onOpenChange={handleDropdownVisibilityChange}
                overlay={renderDropdownMenu(workspaceList)}
                trigger={['click']}
              >
                <MenuItemDiv className="WorkspaceTab-dropdown" isSelected={false}>
                  <SVGWrapper shouldRotate={state.isTabsDropdownVisible}>
                    <Down />
                  </SVGWrapper>
                </MenuItemDiv>
              </Dropdown>
            </DndProvider>
          )}
          {deletionListLength > 0 && (
            <MenuItemDiv
              className="TUTORIAL_WorkspaceNav-Add WorkspaceTab-addWorkspace TabShadow-left"
              isRed={true}
              isSelected={false}
            >
              <DeletionList
                cancelDeletion={proContext.cancelWorkspacesDelete}
                deletionCount={deletionListLength}
                pauseDeletion={proContext.pauseWorkspacesDelete}
                resumeDeletion={proContext.resumeWorkspacesDelete}
              />
            </MenuItemDiv>
          )}
        </>
      )}
    </>
  );
};

const IconWrapper = styled.div`
  svg path {
    fill: ${props => props.theme.colors.foreground};
  }
`;

const SVGWrapper = styled.div<{ shouldRotate: boolean }>`
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  svg {
    transform: rotate(${props => (props.shouldRotate ? '-180deg' : '0deg')});
    path {
      fill: ${props => props.theme.colors.foreground} !important;
    }
  }
`;

const Separator = styled.div`
  margin: 0px 1px;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const Mark = styled.div<{ dontDisplayMarker?: boolean }>`
  height: 70%;
  width: 1px;
  background-color: ${props => (props.dontDisplayMarker ? 'transparent' : props.theme.colors.borderLight)};
`;

const WorkspacesHolder = styled.div`
  &:hover {
    ${Mark} {
      background-color: ${props => props.theme.colors.borderLight};
    }
  }
`;

const MenuItemDiv = styled.div<{ isSelected: boolean; isRed?: boolean }>`
  display: flex;
  flex-grow: 0;
  font-size: 1rem;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 2rem;
  &:hover {
    background-color: ${props => props.theme.colors.backgroundInactive};
  }

  ${props => {
    if (props.isSelected) {
      return css`
        background-color: ${props.theme.colors.backgroundInactive};
        svg {
          color: ${props.theme.colors.foregroundActive};
        }
      `;
    } else if (props.isRed) {
      return css`
        background-color: ${`${props.theme.colors.danger}50`};
        svg {
          color: ${props.theme.colors.foregroundActive};
        }
      `;
    } else {
      return css`
        background-color: ${props.theme.colors.background};
        svg {
          color: ${props.theme.colors.foregroundInactive};
        }
      `;
    }
  }}
`;

const mapStateToProps = (state: RootState): ReduxState => ({
  activeWorkspace: selectActiveWorkspace(state),
  isWindowWidthLargerThenSmall: isWindowWidthLargerThenSmall(state),
  selectedWorkspaceId: selectActiveWorkspaceId(state),
  userWorkspaces: selectUserWorkspaces(state),
  widgetsById: selectWidgets(state),
  workspaces: selectWorkspaces(state),
});

const mapDispatchToProps = (dispatch: ThunkDispatch<RootState, void, RootAction>): DispatchableActions =>
  bindActionCreators(
    {
      addWorkspace,
      switchWorkspace,
    },
    dispatch,
  );

export default connect<ReduxState, DispatchableActions, OwnProps, RootState>(
  mapStateToProps,
  mapDispatchToProps,
)(WorkspaceNav);
