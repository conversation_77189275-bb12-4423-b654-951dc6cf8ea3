import { CSSProperties } from 'react';
import { MosaicNode } from 'react-mosaic-component';

import { WidgetId } from '@benzinga/widget-tools';
import { Widget } from '../../entities/widgetEntity';

export type WorkspaceId = string;

export type PaneSize = Pick<CSSProperties, 'flexBasis' | 'flexGrow'>;

export interface BaseWorkspace {
  name: string;
  version: number;
  workspaceId: WorkspaceId;
}

export interface LegacyWorkspace {
  id: string;
  name: string;
  splitPaneSizes: number;
  widgets: Widget[];
}

export interface WorkspaceV1 extends BaseWorkspace {
  isActive: boolean;
  isHidden: boolean;
  lastAccessed: number | null;
  name: string;
  splitPaneSizes: PaneSize[];
  version: 1;
  widgetIds: WidgetId[];
  workspaceId: WorkspaceId;
}

export interface WorkspaceV2 extends BaseWorkspace {
  config: MosaicNode<WidgetId> | null;
  isActive: boolean;
  isHidden: boolean;
  lastAccessed: number | null;
  name: string;
  version: 2;
  workspaceId: WorkspaceId;
}

export interface WorkspaceV3 extends BaseWorkspace {
  config: MosaicNode<WidgetId> | null;
  lastAccessed: number | null;
  name: string;
  version: 3;
  workspaceId: WorkspaceId;
  isEditable?: boolean;
}

export type HelpPanelItems = string;
export interface WelcomePanel {
  panel: 'welcome';
}
export interface HelpPanel {
  panel: 'help';
  selectedItem: HelpPanelItems;
}

export type WelcomePagePanel = WelcomePanel | HelpPanel;
export interface WelcomePageWorkspace {
  isOnPlatformBar: boolean;
  lastAccessed: number | null;
  currentPanel: WelcomePagePanel;
}
export interface MorningReportPageWorkspace {
  isOnPlatformBar: boolean;
  lastAccessed: number | null;
}

export type Workspace = WorkspaceV3;

export type WorkspacesV1<W extends BaseWorkspace> = {
  list: W[];
};

export type WorkspacesV2<W extends BaseWorkspace> = {
  activeWorkspaceId: WorkspaceId;
  list: W[];
  workspaceIds: WorkspaceId[];
};

export type WorkspacesV3 = {
  activeWorkspaceId: WorkspaceId;
  'home-page': WorkspaceV3;
  userWorkspaces: WorkspaceV3[];
};

export type WorkspacesV4 = WorkspacesV3 & {
  'welcome-page': WelcomePageWorkspace;
  'morning-report': MorningReportPageWorkspace;
};

export enum PreDefinedWorkspaces {
  MorningReport = 'morning-report',
}

export type Workspaces = WorkspacesV4;
