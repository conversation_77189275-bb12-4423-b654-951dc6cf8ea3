import React from 'react';
import { selectWorkspaceById } from '../workspaceSelectors';
import { RootState } from '../../../redux/types';
import { useSelector } from 'react-redux';
import { MorningReport } from './MorningReport';
import styled from '@benzinga/themetron';
import { PreDefinedWorkspaces } from '../workspaceEntity';

interface Props {
  className?: string;
}

const MorningReportWorkspace: React.FC<Props> = props => {
  const morningReportWorkspace = useSelector((state: RootState) =>
    selectWorkspaceById(state, { workspaceId: PreDefinedWorkspaces.MorningReport }),
  );

  if (!morningReportWorkspace) {
    return null;
  }

  return (
    <Holder className={props.className}>
      <MorningReport />
    </Holder>
  );
};

const Holder = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.border};
`;

export default MorningReportWorkspace;
