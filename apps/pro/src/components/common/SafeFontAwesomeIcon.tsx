import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { FontAwesomeIconProps } from '@fortawesome/react-fontawesome';

/**
 * A wrapper for FontAwesomeIcon that adds safety checks for React 19 compatibility
 * This prevents the "Cannot read properties of undefined (reading 'split')" error
 */
export const SafeFontAwesomeIcon: React.FC<FontAwesomeIconProps> = props => {
  // Ensure className is always a string
  const safeProps = {
    ...props,
    className: props.className || '',
  };

  return <FontAwesomeIcon {...safeProps} />;
};

export default SafeFontAwesomeIcon;
