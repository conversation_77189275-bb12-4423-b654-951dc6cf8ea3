import { PersistS<PERSON>age<PERSON><PERSON>, SessionStorageKey } from '@benzinga/fission';
import { AuthenticationManager, getSessionSingleton, Session, LoggingManager } from '@benzinga/session';
import { DateTime } from 'luxon';
import { isEmpty } from 'ramda';
import fastStringify from 'fast-json-stable-stringify';

import {
  AllLayoutData,
  ChatState,
  LayoutData,
  LayoutDataLegacy,
  LayoutDataV10,
  LayoutDataV11,
  LayoutDataV18,
  LayoutDataV19,
  LayoutDataV25,
  LayoutDataV26,
  LayoutId,
  WorkspacesById,
} from '../entities/layoutEntity';
import { Settings, ThemeCategory } from '../entities/settingsEntity';
import { Widget, WidgetId, WidgetV1, Widgets, WidgetsById, WidgetsByIdV1 } from '../entities/widgetEntity';
import {
  PreDefinedWorkspaces,
  Workspace,
  Workspaces,
  WorkspacesV2,
  WorkspaceV1,
  WorkspaceV2,
  WorkspaceV3,
} from '../components/dashboard/workspaceEntity';
import { Thunk } from '../redux/types';
import { generateWidgetId, generateWorkspaceId } from '../utils/entities';
import { getCorrectWidgetsById } from '../utils/layout';
import { USER_SETTINGS } from '../reducers/settingsReducer';
import { extractFromSessionStorage } from '@benzinga/frontend-utils';
import { getInitialWorkspaceState } from '../utils/workspaces';
import { MosaicNode } from 'react-mosaic-component';
import {
  addWidgetToConfig,
  getWidgetsIdsFromConfig,
  removeWidgetFromConfig,
} from '../components/dashboard/workspaceUtils';
import { homePageWorkspace } from '../components/dashboard/homeMenu/homeMenu';
import { Layout, LayoutManager } from '@benzinga/layout-manager';
import { TimeManager } from '@benzinga/time-manager';
import { toQueryFiltersObject } from '@benzinga/legacy-scanner-manager';
import { StoryColorTheme } from '@benzinga/pro-newsfeed-widget';

// Importing Actions
import { Manifest } from '../manifest';
import { LAYOUT_VERSION as LAYOUT_VERSION_MANAGER } from '@benzinga/layout-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { DesktopNotificationManager } from '@benzinga/desktop-notification-manager';
import { SCANNER_PRESET, ScannerConfig } from '@benzinga/scanner-config-manager';
import { NewsConfigManager } from '@benzinga/news-user-settings';
import { ProUserSettingsManager } from '@benzinga/pro-user-settings';
import { selectSession } from '../selectors/sessionSelectors';
import { PlatformBarTabName } from '../components/dashboard/PlatformBar';

// The version of the stored layout.
// Increment this whenever something needs
// to be changed when loading an old layout.
export const LAYOUT_VERSION: LayoutData['layoutVersion'] = LAYOUT_VERSION_MANAGER;
export const LIMIT_MAX_LAYOUT_VERSION = true;
export const NEW_LAYOUT = 'NEW_LAYOUT';
interface NewLayoutAction {
  type: typeof NEW_LAYOUT;
}
export const newLayout = (): Thunk<void> => (dispatch, getState) => {
  const newLayoutAction: NewLayoutAction = {
    type: NEW_LAYOUT,
  };
  dispatch(newLayoutAction);
  selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('add_layout', {});
};

// this would allow us to keep version bumping to minimum during layout refactoring
// after that is complete we should bump to v17 and remove this function
export const translateNewLayout = async (layout: AllLayoutData, _session: Session): Promise<LayoutData> => {
  const translateLayout = async (layout: AllLayoutData): Promise<LayoutData> => {
    switch (layout.layoutVersion) {
      case 10: {
        const { settings, widgets, workspaces, ...rest } = layout as LayoutDataV10;

        const createWorkspacesInfo = (workspaceList: WorkspaceV1[]): WorkspacesV2<WorkspaceV1> =>
          workspaceList.reduce(
            (info, workspace) => ({
              activeWorkspaceId: workspace.isActive ? workspace.workspaceId : info.activeWorkspaceId,
              list: workspaceList,
              workspaceIds: [...info.workspaceIds, workspace.workspaceId],
            }),
            { activeWorkspaceId: '', list: workspaceList, workspaceIds: [] } as WorkspacesV2<WorkspaceV1>,
          ) ?? [];

        const createWidgetsById = (widgetList: WidgetV1[] = []): WidgetsByIdV1 =>
          widgetList.reduce((widgetsById, widget) => ({ ...widgetsById, [widget.widgetId]: widget }), {});

        const createWorkspacesById = (workspaceList: WorkspaceV1[] = []): WorkspacesById<WorkspaceV1> =>
          workspaceList.reduce((wById, workspace) => ({ ...wById, [workspace.workspaceId]: workspace }), {});

        if (settings.newsfeed) {
          // this is necessary because having `null` in the newsfeed themes can break the newsfeed
          settings.newsfeed.themes = settings.newsfeed.themes.filter(theme => theme);
          // restore and dedup themes
          settings.newsfeed.themes = settings.newsfeed.themes.reduce(
            (restoredThemes: any[], theme: StoryColorTheme) => {
              if (
                theme.target &&
                (theme.target as unknown as { id: string })?.id &&
                !restoredThemes.some(
                  restoredTheme => restoredTheme.target.id === (theme.target as unknown as { id: string })?.id,
                )
              ) {
                if ((theme.category as ThemeCategory | null | 'channel') === 'channel') {
                  theme.category = ThemeCategory.Category;
                }
                restoredThemes.push(theme);
              }
              return restoredThemes;
            },
            [],
          );
        }

        return translateLayout(
          widgets?.list && workspaces?.list
            ? ({
                ...rest,
                layoutVersion: 11,
                settings,
                widgets,
                widgetsById: createWidgetsById(widgets.list),
                workspaces: createWorkspacesInfo(workspaces.list),
                workspacesById: createWorkspacesById(workspaces.list),
              } as LayoutDataV11)
            : (layout as unknown as LayoutDataV11),
        );
      }
      case 11: {
        const workspacesById = Object.values(layout.workspacesById).reduce<WorkspacesById<WorkspaceV1>>(
          (accumulator, workspace) => {
            const newWorkspaceId = generateWorkspaceId();

            if (workspace.workspaceId === layout.workspaces.activeWorkspaceId) {
              layout.workspaces.activeWorkspaceId = newWorkspaceId;
            }

            const index = layout.workspaces.workspaceIds.findIndex(
              workspaceId => workspaceId === workspace.workspaceId,
            );
            if (index !== -1) {
              layout.workspaces.workspaceIds[index] = newWorkspaceId;
            }

            workspace.widgetIds = workspace.widgetIds.filter(widgetId => {
              const shouldKeep = Object.keys(layout.widgetsById).includes(widgetId);

              if (!shouldKeep) {
                _session.getManager(TrackingManager).trackErrorEvent('emit', {
                  error_message: `Removed invalid widgetId reference: ${widgetId} - ${layout.layoutVersion}`,
                });
              }

              return shouldKeep;
            });
            workspace.workspaceId = newWorkspaceId;
            accumulator[newWorkspaceId] = workspace;
            return accumulator;
          },
          {},
        );

        const widgetsById = Object.values(layout.widgetsById).reduce<WidgetsByIdV1>((accumulator, widget) => {
          const newWidgetId = generateWidgetId();

          // used lodash forEach because of side effect inside.
          Object.values(workspacesById).forEach(workspace => {
            const index = workspace.widgetIds.findIndex(widgetId => widgetId === widget.widgetId);
            if (index !== -1) {
              workspace.widgetIds[index] = newWidgetId;
            }
          });

          widget.widgetId = newWidgetId;
          accumulator[newWidgetId] = widget;
          return accumulator;
        }, {});
        return translateLayout({
          ...layout,
          layoutVersion: 12,
          widgetsById,
          workspacesById,
        });
      }
      case 12:
      case 13: {
        Object.values(layout.workspacesById).forEach(workspace => {
          workspace.lastAccessed = null;
        });

        return translateLayout({
          ...layout,
          layoutVersion: 14,
        });
      }
      case 14:
      case 15: {
        const WidgetTypeMapper = {
          advancedCalendar: 'calendar',
        };

        Object.values(layout.widgetsById).forEach(widget => {
          widget.type = WidgetTypeMapper[widget.type] ?? widget.type;
        });

        if (layout.settings.newsfeed) {
          layout.settings.newsfeed.themes = layout.settings.newsfeed.themes.reduce((acc, theme) => {
            if (theme.category !== 'category') {
              switch (theme.category) {
                case 'source':
                  return [...acc, { ...theme, target: (theme.target as unknown as { id: string }).id }];
                case 'watchlist':
                  return [
                    ...acc,
                    { ...theme, target: (theme.target as unknown as { watchlistId: string }).watchlistId },
                  ];

                default:
                  return [...acc, theme];
              }
            }
            return acc;
          }, [] as StoryColorTheme[]);
        }
        return translateLayout({
          ...layout,
          layoutVersion: 16,
        });
      }
      case 16:
      case 17: {
        const workspacesById = layout.workspacesById
          ? Object.entries(layout.workspacesById).reduce<WorkspacesById<WorkspaceV2>>((acc, [id, workspace]) => {
              if (id === '_persist') {
                return acc;
              }
              return {
                ...acc,
                [workspace.workspaceId]: {
                  ...workspace,
                  // workspace.widgetIds? because of _persist
                  config: workspace.widgetIds?.reduce<MosaicNode<WidgetId> | null>(
                    (acc, widgetId) => addWidgetToConfig(widgetId, acc),
                    null,
                  ),
                  version: 2,
                },
              };
            }, {})
          : layout.workspaces.list.reduce(
              (acc, workspace) => ({
                ...acc,
                [workspace.workspaceId]: {
                  ...workspace,
                  // workspace.widgetIds? because of _persist
                  config: workspace.widgetIds?.reduce<MosaicNode<WidgetId> | null>(
                    (acc, widgetId) => addWidgetToConfig(widgetId, acc),
                    null,
                  ),
                  version: 2,
                },
              }),
              {},
            );
        const workspaceList = Object.values(workspacesById);

        return translateLayout({
          ...layout,
          layoutVersion: 18,
          widgets: {
            list: Object.values(layout.widgetsById),
          },
          workspaces: {
            ...layout.workspaces,
            list: workspaceList,
          },
          workspacesById,
        } as LayoutDataV18);
      }

      case 18: {
        const userWorkspaces = (layout.workspaces?.list ?? Object.values(layout.workspacesById) ?? []).map<WorkspaceV3>(
          w => ({
            ...w,
            version: 3,
          }),
        );
        const userWorkspacesById = userWorkspaces.reduce<WorkspacesById<WorkspaceV3>>((acc, workspace) => {
          acc[workspace.workspaceId] = workspace;
          return acc;
        }, {});

        return translateLayout({
          ...layout,
          layoutVersion: 19,
          widgets: {
            list: layout.widgets?.list ?? Object.values(layout.widgetsById),
          },
          workspaces: {
            activeWorkspaceId: layout.workspaces.activeWorkspaceId,
            'home-page': homePageWorkspace,
            userWorkspaces: userWorkspaces,
          },
          workspacesById: userWorkspacesById,
        } as LayoutDataV19);
      }
      case 19: {
        return translateLayout({
          ...layout,
          layoutVersion: 20,
          widgets: {
            list: layout.widgets?.list ?? Object.values((layout as any).widgetsById),
          },
        });
      }
      case 20: {
        // the main point of this version is to move scannerConfigs from local storage to be saved in layout
        const data = window.localStorage.getItem('scannerLayouts');
        const toScannerConfig = (config: ScannerConfig): ScannerConfig => {
          return {
            ...config,
            filters: toQueryFiltersObject(config.filters),
          };
        };

        const scannerConfig: ScannerConfig[] = [...SCANNER_PRESET, ...JSON.parse(data ?? '[]')].map(toScannerConfig);

        return translateLayout({
          ...layout,
          layoutVersion: 21,
          scannerConfigs: scannerConfig,
          settings: {
            ...layout.settings,
            scanner: {
              defaultConfig: scannerConfig[0].uuid,
            },
          },
        });
      }
      case 21: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { scannerConfigs: _scannerConfigs, ...oldLayout } = layout;
        // scanner Configs are now stored in user settings
        return translateLayout({
          ...oldLayout,
          layoutVersion: 22,
          settings: {
            ...oldLayout.settings,
            calendar: {
              dateFilterPreference: oldLayout.settings.calendar?.dateFilterPreference ?? 'keepUserDates',
            },
          },
        });
      }

      case 22:
        return translateLayout({
          ...layout,
          layoutVersion: 23,
          workspaces: {
            ...layout.workspaces,
            'morning-report': { isOnPlatformBar: false, lastAccessed: null },
            'welcome-page': { currentPanel: { panel: 'welcome' }, isOnPlatformBar: false, lastAccessed: null },
          },
        });

      case 23:
        return translateLayout({
          ...layout,
          layoutVersion: 24,
          settings: {
            ...layout.settings,
            newsfeed: {
              ...layout.settings.newsfeed,
              disableResponsiveDisplay: false,
            },
          },
        });

      case 24: {
        if (typeof layout?.settings?.newsfeed?.dontShowTodaysMarketMovingNewsDialog === 'boolean') {
          getSessionSingleton()
            .getManager(NewsConfigManager)
            .setDontShowMarketMovingNews(layout.settings.newsfeed.dontShowTodaysMarketMovingNewsDialog);
          delete layout.settings.newsfeed['dontShowTodaysMarketMovingNewsDialog'];
        }
        return translateLayout({
          ...layout,
          layoutVersion: 24.1,
        });
      }
      case 24.1: {
        const {
          settings: { calendar, chartInfo, newsfeed, scanner, ...settings },
          ...oldLayout
        } = layout;

        // we did some thing stupid where the version on production and probeta were mismatched. this is a hack to fix it
        return translateLayout(
          (layout.settings as any).widgetGlobalSettings
            ? {
                ...(layout as unknown as LayoutDataV25),
                layoutVersion: 25,
              }
            : {
                ...oldLayout,
                layoutVersion: 25,
                settings: {
                  ...settings,
                  widgetGlobalSettings: {
                    advancedNewsfeed: {
                      allCaps: newsfeed.allCaps,
                      disableResponsiveDisplay: newsfeed.disableResponsiveDisplay ?? false,
                      displayDateBanner: newsfeed.displayTodaysDay,
                      displayOrOperatorInExpression: newsfeed.displayOrOperatorInExpression,
                      displayType: newsfeed.newsfeedDisplayType,
                      headerSpacing: newsfeed.headerSpacing,
                      highlightBeatsMisses: newsfeed.highlightBeatsMisses,
                      highlightDollarAmounts: newsfeed.highlightDollarAmounts,
                      highlightPercentages: newsfeed.highlightPercentages,
                      highlightQuarters: newsfeed.highlightQuarters,
                      textSize: newsfeed.textSize,
                      themes: newsfeed.themes,
                    },
                    calendar,
                    chart: {
                      chartInfo,
                      globals: settings.chartGlobals,
                    },
                    scanner,
                  },
                },
              },
        );
      }
      case 25:
        return translateLayout({
          ...layout,
          layoutVersion: 26,
          settings: {
            ...layout.settings,
            // we did some thing stupid where the version on production and probeta were mismatched. this is a hack to fix it
            infoBarEnabled:
              (layout as unknown as LayoutDataV26).settings.infoBarEnabled ?? layout.settings.tickerBarEnabled,
          },
        });

      case 26: {
        const {
          settings: { ...settings },
          ...oldLayout
        } = layout;

        return translateLayout({
          ...oldLayout,
          layoutVersion: 27,
          settings: {
            ...settings,
            widgetGlobalSettings: {
              ...settings.widgetGlobalSettings,
              chart: {
                ...settings.widgetGlobalSettings.chart,
                defaultChartId: '',
                defaultDrawingCanvasId: '',
              },
            },
          },
        });
      }
      case 27:
        return translateLayout({
          ...layout,
          layoutVersion: 28,
          settings: {
            ...layout.settings,
            globalChatWidgetId: layout.settings.streamVisible
              ? layout.widgets.list.find(w => w.type === 'stream')?.widgetId
              : undefined,
            narrateSquawkStatus: true,
            streamDisplayPosition: 'left',
            widgetGlobalSettings: {
              ...layout.settings.widgetGlobalSettings,
              chat: {
                chatMediaPreferencesSettings: {
                  allowFileImages: layout.chat.allowFileImages,
                  allowFileVideo: layout.chat.allowFileVideo,
                  allowLinkedFiles: layout.chat.allowLinkedFiles,
                  allowSpecialUserHighlighting: layout.chat.allowSpecialUserHighlighting,
                  allowURL: layout.chat.allowURL,
                },
                chatroomState: layout.chat.chatroomState,
                defaultChannel: layout.chat.defaultChannel,
                defaultChannelState: layout.chat.defaultChannelState,
                messageLayout: layout.chat.messageLayout,
                subscribedChannelIDs: layout.chat.subscribedChannelIDs,
              },
            },
          },
        });
      case 28:
        return translateLayout({
          ...layout,
          layoutVersion: 29,
          settings: {
            ...layout.settings,
            globalChatWidgetId: layout.settings.globalChatWidgetId ?? null,
            mobileWidgetId: null,
          },
        });
      case 29:
        return translateLayout({
          ...layout,
          layoutVersion: 30,
          settings: {
            ...layout.settings,
            globalChatWidgetId: layout.widgets.list.find(w => w.widgetId === layout.settings.globalChatWidgetId)
              ? layout.settings.globalChatWidgetId
              : null,
            mobileWidgetId: layout.widgets.list.find(w => w.widgetId === layout.settings.mobileWidgetId)
              ? layout.settings.mobileWidgetId
              : null,
          },
        });
      case 30:
        return translateLayout({
          ...layout,
          layoutVersion: 31,
          proHash: process.env.GIT_COMMITHASH ?? 'unknown',
          proVersion: process.env.RELEASE_VERSION ?? 'unknown',
        });
      case 31:
        return translateLayout({
          ...layout,
          layoutVersion: 32,
          settings: {
            ...layout.settings,
            widgetVersions: layout.widgets.list.reduce(
              (acc, w) => ({ ...acc, [w.type]: w.version }),
              Manifest.reduce((acc, m) => {
                return {
                  ...acc,
                  [m.id]: m.version,
                };
              }, {}),
            ),
          },
          widgets: {
            list: layout.widgets.list.map(w => ({
              parameters: w.data.parameters,
              type: w.type,
              widgetId: w.widgetId,
            })),
          },
        });
      case 32:
        return translateLayout({
          ...layout,
          layoutVersion: 33,
          settings: {
            ...layout.settings,
            globalNotificationWidgetId: layout.widgets.list.find(w => w.type === 'notification')?.widgetId ?? null,
          },
        });
      case 33: {
        const proUserSettingsManager = getSessionSingleton().getManager(ProUserSettingsManager);
        const currentSettings = proUserSettingsManager.getOldSettings();
        const moveSetting: ProUserSettingsManager['setSetting'] = (key, val) => {
          delete (layout.settings as any)[key];
          if (!currentSettings || !(key in currentSettings)) {
            proUserSettingsManager.setSetting(key, val);
          }
          return;
        };

        moveSetting('colorBlindMode', layout.settings.colorBlindMode);
        moveSetting('notificationsEnabled', layout.settings.notificationsEnabled);
        moveSetting('sidebarVisible', layout.settings.sidebarVisible);
        moveSetting('timezone', layout.settings.timezone);
        moveSetting('timeFormat', layout.settings.timeFormat);
        moveSetting('infoBarEnabled', layout.settings.infoBarEnabled);

        return translateLayout({
          ...layout,
          layoutVersion: 34,
          settings: {
            ...layout.settings,
          },
        });
      }
      case 34:
        return translateLayout({
          ...layout,
          layoutVersion: 35,
          settings: {
            ...layout.settings,
            initialMorningUpdateLoad: false,
          },
        });
      case 35:
        return layout;
    }
  };
  return translateLayout(layout);
};

const isLegacyLayout = (layout: any): layout is LayoutDataLegacy => typeof layout.layoutVersion !== 'number';

const translateLayout = async (
  layout: LayoutDataLegacy | Parameters<typeof translateNewLayout>[0],
  session: Session,
): Promise<LayoutData> => {
  if (isLegacyLayout(layout)) {
    throw Error('layout is to old to update');
  } else {
    return await translateNewLayout(layout, session);
  }
};

// todo - refactor, use restoreOldLayout as example
const restoreLayout = async (layout: AllLayoutData, session: Session): Promise<LayoutData> => {
  const translatedLayout = (await translateLayout(layout, session)) as LayoutData;
  Object.values(translatedLayout).forEach(value => {
    if (value?.['_persist']) {
      delete value['_persist'];
    }
  });

  const { settings = USER_SETTINGS, widgets, workspaces } = translatedLayout;

  // make sure the widget exists if not remove it from workspace
  let widgetsById = widgets.list.reduce<WidgetsById>((acc, w) => {
    acc[w.widgetId] = w;
    return acc;
  }, {});
  const widgetIds = Object.keys(widgetsById);
  const workspacesById = workspaces.userWorkspaces.reduce<WorkspacesById<Workspace>>((acc, workspace) => {
    const invalidWidgets = getWidgetsIdsFromConfig(workspace.config).filter(
      widgetId =>
        !(
          widgetIds.includes(widgetId) &&
          Manifest.find(m => m.id === widgetsById?.[widgetId].type) &&
          widgetId !== settings.globalChatWidgetId
        ),
    );
    invalidWidgets.forEach(widgetId => {
      workspace.config = removeWidgetFromConfig(widgetId, workspace.config);
    });

    return {
      ...acc,
      [workspace.workspaceId]: workspace,
    };
  }, {});

  const groupedWidgetByType = Object.values(widgetsById).reduce<Record<string, Widget[]>>(
    (acc, widget) => ({ ...acc, [widget.type]: acc[widget.type] ? [...acc[widget.type], widget] : [widget] }),
    {},
  );

  const groupedByType = Manifest.reduce<Record<string, Widget[]>>(
    (acc, m) => ({
      ...acc,
      [m.id]: groupedWidgetByType[m.id] ?? [],
    }),
    {},
  );

  const updatedWidgets = await Object.entries(groupedByType).reduce(
    async (
      pAcc,
      [type, widgets],
    ): Promise<{
      widgetVersions: Record<string, number>;
      widgetGlobalSettings: Record<string, object>;
      widgetsById: Record<string, Widget>;
    }> => {
      const acc = await pAcc;
      const manifest = Manifest.find(m => m.id === type);

      if (manifest) {
        const getDefaultGlobalParameters = async () => {
          try {
            const a = manifest.initGlobalParameters?.(
              window?.env?.WIDGET_GLOBAL_PARAMETERS[manifest.id] ?? {},
              session,
            );
            if (a) {
              if (a instanceof Promise) {
                return await a;
              } else {
                return a;
              }
            }
          } catch (e) {
            session.getManager(TrackingManager).trackLayoutEvent('error', {
              error_message: `exception while creating init global parameters for ${manifest.name} reverting to default`,
            });
            session.getManager(LoggingManager).log(
              'error',
              {
                category: manifest.id,
                message: `Could not create default global parameters using init function for ${manifest.name} reverting to default`,
              },
              ['console'],
            );
          }
          return manifest.defaultGlobalParameters;
        };

        const getDefaultWidgetParameters = async (globalParameters: any) => {
          try {
            const a = (manifest.initWidgetParameters as any)?.(undefined, globalParameters, session) as any;
            if (a) {
              if (a instanceof Promise) {
                return await a;
              } else {
                return a;
              }
            }
          } catch (e) {
            session.getManager(TrackingManager).trackLayoutEvent('error', {
              error_message: `exception while creating init parameters for ${manifest.name} reverting to default`,
            });
            session.getManager(LoggingManager).log(
              'error',
              {
                category: manifest.id,
                message: `Could not create default parameters for using init function ${manifest.name} reverting to default`,
              },
              ['console'],
            );
          }
          return manifest.defaultWidgetParameters;
        };

        try {
          const updated = await (
            manifest.migrator as (config: any, session: Session) => ReturnType<(typeof Manifest)[0]['migrator']>
          )(
            {
              globalSettings: settings.widgetGlobalSettings[type] ?? (await getDefaultGlobalParameters()),
              version: settings.widgetVersions[type] ?? manifest.version,
              widgets,
            },
            session,
          );

          return {
            ...acc,
            widgetGlobalSettings: {
              ...acc.widgetGlobalSettings,
              [type]: updated.globalSettings ?? (await getDefaultGlobalParameters()),
            },
            widgetVersions: { ...acc.widgetVersions, [type]: manifest.version },
            widgetsById: {
              ...acc.widgetsById,
              ...(updated.widgets as any[]).reduce<Record<string, Widget>>(
                (acc, w) => ({
                  ...acc,
                  [w.widgetId]: {
                    parameters: w.parameters,
                    type: manifest.id,
                    widgetId: w.widgetId,
                  },
                }),
                {},
              ),
            },
          };
        } catch (e) {
          session.getManager(TrackingManager).trackLayoutEvent('error', {
            error_message: `exception while migrating ${manifest.id} Reverting to default`,
          });
          session.getManager(LoggingManager).log(
            'error',
            {
              category: manifest.id,
              message: `Could not migrate parameters for ${manifest.name} reverting to default`,
            },
            ['console', 'toast'],
          );

          const defaultGlobalParameters = await getDefaultGlobalParameters();
          const defaultParameters = await getDefaultWidgetParameters(defaultGlobalParameters);
          return {
            ...acc,
            widgetGlobalSettings: {
              ...acc.widgetGlobalSettings,
              [type]: defaultGlobalParameters,
            },
            widgetVersions: { ...acc.widgetVersions, [type]: manifest.version },
            widgetsById: {
              ...acc.widgetsById,
              ...(widgets as any[]).reduce<Record<string, Widget>>(
                (acc, w) => ({
                  ...acc,
                  [w.widgetId]: {
                    parameters: defaultParameters,
                    type: manifest.id,
                    widgetId: w.widgetId,
                  },
                }),
                {},
              ),
            },
          };
        }
      } else {
        return {
          ...acc,
          widgetsById: {
            ...acc.widgetsById,
            ...widgets.reduce<Record<string, Widget>>((acc, w) => ({ ...acc, [w.widgetId]: w }), {}),
          },
        };
      }
    },
    Promise.resolve({ widgetGlobalSettings: {}, widgetVersions: {}, widgetsById: {} }),
  );

  // catching no-valid workspaces error
  const isActiveWorkspaceExists =
    workspaces.activeWorkspaceId === 'home-page' ||
    workspaces.activeWorkspaceId === 'welcome-page' ||
    workspaces.activeWorkspaceId === 'morning-report' ||
    Object.keys(workspacesById).includes(workspaces.activeWorkspaceId);
  const hasWorkspaces = !isEmpty(workspacesById);

  if (settings.initialMorningUpdateLoad) {
    if (!workspaces.userWorkspaces.find((w: Workspace) => w.workspaceId === 'morning-report')) {
      workspacesById[PreDefinedWorkspaces.MorningReport] = {
        config: null,
        isEditable: false,
        lastAccessed: null,
        name: PlatformBarTabName.MorningReport,
        version: 3,
        workspaceId: PreDefinedWorkspaces.MorningReport,
      };
    }

    workspaces.activeWorkspaceId = PreDefinedWorkspaces.MorningReport;
  } else if (!isActiveWorkspaceExists) {
    session.getManager(DesktopNotificationManager).showInternalNotification('Invalid workspaces', {
      body: `One or more workspaces were invalid. We've restored intact workspaces and your settings.`,
    });

    if (!hasWorkspaces) {
      workspaces.activeWorkspaceId = workspaces['welcome-page'].isOnPlatformBar ? 'welcome-page' : 'home-page';
      widgetsById = {};
    } else {
      const existsWidgetsInWorkspaces = Object.keys(workspacesById)
        .map(key => getWidgetsIdsFromConfig(workspacesById[key].config))
        .flat();
      workspaces.activeWorkspaceId = workspaces['welcome-page'].isOnPlatformBar ? 'welcome-page' : 'home-page';
      widgetsById = Object.values(widgetsById).reduce<WidgetsById>((acc, widget) => {
        if (existsWidgetsInWorkspaces.includes(widget.widgetId)) {
          acc[widget.widgetId] = widget;
        }
        return acc;
      }, {});
    }
  }

  const correctWidgetsIds: string[] = [];

  Object.values(workspacesById).forEach(workspace => {
    correctWidgetsIds.push(...getWidgetsIdsFromConfig(workspace.config));
    // ensure the current selected workspace gets it's lastAccessed updated
    if (workspace.workspaceId === workspaces.activeWorkspaceId) {
      workspace.lastAccessed = DateTime.local().toMillis();
    }
  });
  settings.globalChatWidgetId && correctWidgetsIds.push(settings.globalChatWidgetId);
  settings.globalNotificationWidgetId && correctWidgetsIds.push(settings.globalNotificationWidgetId);
  return {
    layoutVersion: LAYOUT_VERSION,
    proHash: process.env.GIT_COMMITHASH ?? 'unknown',
    proVersion: process.env.RELEASE_VERSION ?? 'unknown',
    settings: {
      ...settings,
      globalChatWidgetId:
        settings.globalChatWidgetId && Object.keys(widgetsById).includes(settings.globalChatWidgetId)
          ? settings.globalChatWidgetId
          : null,
      globalNotificationWidgetId:
        settings.globalNotificationWidgetId && Object.keys(widgetsById).includes(settings.globalNotificationWidgetId)
          ? settings.globalNotificationWidgetId
          : null,
      mobileWidgetId:
        settings.mobileWidgetId && Object.keys(widgetsById).includes(settings.mobileWidgetId)
          ? settings.mobileWidgetId
          : null,
      widgetGlobalSettings: { ...settings.widgetGlobalSettings, ...updatedWidgets.widgetGlobalSettings },
      widgetVersions: { ...settings.widgetVersions, ...updatedWidgets.widgetVersions },
    },
    widgets: {
      list: Object.values(
        getCorrectWidgetsById({ ...widgetsById, ...updatedWidgets.widgetsById }, correctWidgetsIds),
      ) as Widget[],
    },
    workspaces: {
      activeWorkspaceId: workspaces.activeWorkspaceId,
      'home-page': homePageWorkspace,
      'morning-report': {
        isOnPlatformBar:
          (workspaces?.activeWorkspaceId === 'morning-report' || workspaces['morning-report']?.isOnPlatformBar) ??
          false,
        lastAccessed: null,
      },
      userWorkspaces: Object.values(workspacesById),
      'welcome-page': {
        currentPanel: { panel: 'welcome' },
        isOnPlatformBar: workspaces['welcome-page']?.isOnPlatformBar ?? false,
        lastAccessed: null,
      },
    },
  };
};

export const LOAD_LAYOUT = 'LOAD_LAYOUT';
export interface LoadLayoutAction {
  payload: {
    chat?: ChatState;
    settings?: Settings;
    workspaces: Workspaces;
    widgetsById: WidgetsById;
  };
  type: typeof LOAD_LAYOUT;
}

const loadParsedLayout = async (
  session: Session,
  parsedLayout: AllLayoutData,
): Promise<{
  chat?: ChatState;
  settings?: Settings;
  workspaces: Workspaces;
  widgetsById: WidgetsById;
}> => {
  const layout = fastStringify(parsedLayout);
  let newLayout: LayoutData | null = null;
  try {
    newLayout = await restoreLayout(parsedLayout, session);
  } catch (err) {
    session.getManager(LoggingManager).log(
      'error',
      {
        category: 'PlatformBanner',
        data: err,
        message: `Error Loading Layout Reverting to Default.`,
      },
      ['toast', 'console'],
    );
    console.error(err);

    newLayout = await getNewLayout(session);
  }
  const { settings, widgets, workspaces } = newLayout;

  const widgetsById = widgets.list.reduce(
    (widgetsById, widget) => ({
      ...widgetsById,
      [widget.widgetId]: widget,
    }),
    {} as WidgetsById,
  );

  const timeManager = session.getManager(TimeManager);
  const proUserSettings = session.getManager(ProUserSettingsManager);

  timeManager.setTimeFormat(proUserSettings.getSettings().timeFormat);
  timeManager.setTimezone(proUserSettings.getSettings().timezone);

  const payload = { settings, widgetsById, workspaces };

  session.getManager(TrackingManager).trackLayoutEvent('load_layout', {
    layout_id: layout,
  });

  return payload;
};

export const getInitialSettings = async (session: Session | undefined): Promise<Settings> => ({
  ...USER_SETTINGS,
  widgetGlobalSettings: {
    ...Manifest.reduce(async (acc, m) => {
      const params = async () => {
        if (m.initGlobalParameters && session) {
          try {
            const initGlobalParameters = m.initGlobalParameters(
              window?.env?.WIDGET_GLOBAL_PARAMETERS[m.id] ?? {},
              session,
            );
            if (initGlobalParameters instanceof Promise) {
              return await initGlobalParameters;
            } else {
              return initGlobalParameters;
            }
          } catch (e) {
            console.log(`Error getting initGlobalParameters cb for ${m.id} `, e);
            return m.defaultGlobalParameters;
          }
        } else {
          return m.defaultGlobalParameters;
        }
      };

      return {
        ...acc,
        [m.id]: await params(),
      };
    }, {}),
  },
});

export const getNewLayout = async (session: Session): Promise<LayoutData> => ({
  layoutVersion: LAYOUT_VERSION,
  proHash: process.env.GIT_COMMITHASH ?? 'unknown',
  proVersion: process.env.RELEASE_VERSION ?? 'unknown',
  settings: await getInitialSettings(session),
  widgets: { list: [] },
  workspaces: getInitialWorkspaceState(),
});

export const extractLayoutFromPersist = (): AllLayoutData | null => {
  const workspaces = extractFromSessionStorage<any>(PersistStorageKey.workspaces) ?? ({} as Workspaces);
  const widgets = extractFromSessionStorage<Widgets>(PersistStorageKey.widgets) ?? ({} as Widgets);
  const scannerConfigs = extractFromSessionStorage<ScannerConfig[]>(PersistStorageKey.scannerConfigs) ?? [];
  const settings = extractFromSessionStorage<Settings>(PersistStorageKey.settings) ?? USER_SETTINGS;
  const initialized = extractFromSessionStorage<boolean>(PersistStorageKey.initialized) || null;
  const layoutVersion = parseInt(extractFromSessionStorage<string>(PersistStorageKey.layoutVersion) ?? '0') ?? null;

  return (initialized &&
    layoutVersion && {
      layoutVersion,
      scannerConfigs,
      settings,
      widgets,
      workspaces,
    }) as unknown as AllLayoutData;
};

const loadLayout = async (layoutId: string | undefined, session: Session): Promise<LayoutData | null> => {
  if (layoutId) {
    if (layoutId === 'new-layout') {
      return await getNewLayout(session);
    } else {
      const savedLayout = await session.getManager(LayoutManager).getLayout(layoutId);
      return savedLayout.ok?.data ?? null;
    }
  }
  return null;
};

const getUrlLayout = async (session: Session): Promise<LayoutData | null> => {
  const layoutId = new URLSearchParams(window.location.search).get('layoutId');
  if (layoutId) {
    window.history.replaceState(null, '', window.location.pathname);
    if (layoutId === 'new-layout') {
      return await getNewLayout(session);
    } else {
      const savedLayout = await session.getManager(LayoutManager).getLayout(layoutId);
      return savedLayout.ok?.data ?? null;
    }
  }
  return null;
};

const getPastedLayout = (): LayoutData | null => {
  const stringifiedLayout = window.sessionStorage.getItem(SessionStorageKey.pastedLayout);
  if (stringifiedLayout) {
    window.sessionStorage.removeItem(SessionStorageKey.pastedLayout);
    return JSON.parse(stringifiedLayout);
  }
  return null;
};

const getServerLayout = async (session: Session): Promise<LayoutData | null> => {
  const layoutId = window.sessionStorage.getItem(PersistStorageKey.activeLayoutId);
  if (!layoutId) {
    return null;
  }
  window.sessionStorage.removeItem(PersistStorageKey.activeLayoutId);
  const layout = await session.getManager(LayoutManager).getLayout(layoutId);
  return layout.ok?.data ?? null;
};

const getLatestLayout = async (session: Session): Promise<LayoutData | null> => {
  const layouts = (await session.getManager(LayoutManager).getLayoutsList()).ok ?? [];
  const uuid = layouts
    .filter(layout => layout.layout_version <= LAYOUT_VERSION)
    .sort((a, b) => new Date(b.date_updated || 0).getTime() - new Date(a.date_updated || 0).getTime())[0]?.uuid;
  if (uuid) {
    const layout = await session.getManager(LayoutManager).getLayout(uuid);
    return layout.ok?.data || null;
  } else {
    return null;
  }
};

export const loadSavedLayout = async (layoutId: string | undefined, session: Session) => {
  const isLoggedIn = session.getManager(AuthenticationManager).isLoggedIn();
  const layoutPersist = extractLayoutFromPersist();
  const layout =
    (await loadLayout(layoutId, session)) ??
    (await getUrlLayout(session)) ??
    getPastedLayout() ??
    (isLoggedIn ? layoutPersist : undefined) ??
    (await getServerLayout(session)) ??
    (await getLatestLayout(session)) ??
    (await getNewLayout(session));

  return await loadParsedLayout(session, layout);
};

export interface LayoutsStorage {
  [id: string]: Layout<AllLayoutData> | undefined;
}

export const safePutLayout = async (layout: Layout<AllLayoutData>) => {
  const layoutsManager = getSessionSingleton().getManager(LayoutManager);
  return (await layoutsManager.saveLayout(layout)) as Layout<AllLayoutData> | undefined;
};

export const newLayoutFromServer = async (name?: string, layout?: Layout<AllLayoutData>['data']) => {
  const layoutsManager = getSessionSingleton().getManager(LayoutManager);
  return (await layoutsManager.newLayout(name, layout)) as Layout<AllLayoutData> | undefined;
};

export const deleteLayoutOnServer = async (layoutId: LayoutId) => {
  const layoutsManager = getSessionSingleton().getManager(LayoutManager);
  return await layoutsManager.deleteLayout(layoutId);
};

export const sendPersistedLayoutToServer = async (layout: Layout<AllLayoutData>, session: Session) => {
  const data = await translateLayout(layout.data, session);
  return await safePutLayout({ ...layout, data });
};

export const removeChatFromLayout = (layout: AllLayoutData): AllLayoutData => {
  const globalChatWidgetID = (layout?.settings as Settings)?.globalChatWidgetId;
  const filteredWidgetList = layout?.widgets?.list?.filter(
    widget => widget.widgetId !== globalChatWidgetID && widget.type !== 'chat',
  );

  if (layout?.widgets?.list) {
    layout.widgets.list = filteredWidgetList as Widget[];
  }

  return layout;
};

export type LayoutActions = LoadLayoutAction | NewLayoutAction;
