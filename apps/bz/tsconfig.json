{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "lib": ["dom", "esnext"], "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "strictPropertyInitialization": false, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node"]}, "include": ["svgr.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../apps/bz/.next/types/**/*.ts", "../../dist/apps/bz/.next/types/**/*.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.js", "**/*.spec.ts", "**/*.test.ts"]}