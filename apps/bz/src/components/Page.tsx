'use client';
import React, { startTransition, useEffect, useState } from 'react';
import Script from 'next/script';
import { usePathname } from 'next/navigation';
import Modal from 'react-modal';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useIsUserPaywalled } from '@benzinga/user-context';
import { isMobile } from '@benzinga/device-utils';

import {
  MainMenu,
  NavigationHeader,
  NavigationFooter,
  FooterNavigationGroup,
  NavigationMenus,
  FooterLink,
} from '@benzinga/navigation-ui';
import {
  getMenuByLocale,
  getHeaderVariantByLocale,
  getFooterVariantByLocale,
  getLogoVariantByLocale,
} from '@benzinga/identity';
import { AuthenticationManager } from '@benzinga/session';
import { UserManager } from '@benzinga/user-manager';
import { SessionContext } from '@benzinga/session-context';
import { logCommitHash, logFriendlyBenzingaMessageToDevelopers, logReleaseVersion } from '@benzinga/utils';
import { checkDeviceType } from '@benzinga/device-utils';
import { cookieCleaner, formatNavigationSchema } from '@benzinga/frontend-utils';
import { CoralogixRumLoader } from '@benzinga/coralogix-monitoring';
import { PageTracker, GeoTracker, GoogleTagAnalytics, ClickTracker } from '@benzinga/analytics';
import { Meta, MetaProps, Schema } from '@benzinga/seo';
import { AuthPortal, Upsell, PushSubscriber } from '@benzinga/auth-ui';
import { LoadingScreen, ErrorBoundary } from '@benzinga/core-ui';
import Hooks, { NoFirstRender } from '@benzinga/hooks';
import i18n, { LocaleType } from '@benzinga/translate';

import { WordpressPage, WordpressPost } from '@benzinga/content-manager';
import { HeaderProps } from '../entities/header';
import {
  disableOptinMonsterFonts,
  disableOptinMonsterCampaigns,
  raptiveAdManager,
  nativoAdManager,
} from '@benzinga/ads-utils';

if (typeof window !== 'undefined') {
  cookieCleaner();
}

const NativoScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.NativoScript };
  }),
);

const MobileAppBannerAd = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.MobileAppBannerAd };
  }),
);

const TaboolaTrack = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.TaboolaTrack };
  }),
);

// const RaptiveHead = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.RaptiveHead };
//   }),
// );

// const RaptiveFooter = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.RaptiveFooter };
//   }),
// );

// const RaptiveEmailCapture = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.RaptiveEmailCapture };
//   }),
// );

// const NavigationFooter = React.lazy(() =>
//   import('@benzinga/navigation-ui').then(module => {
//     return { default: module.NavigationFooter };
//   }),
// );

try {
  Modal.setAppElement('#__next');
} catch (error) {
  // eslint-disable-next-line no-console
}

export interface PageProps {
  article?: any;
  children: React.ReactNode;
  disablePageTracking?: boolean;
  embeddedWidget?: boolean;
  error?: any;
  headerProps?: HeaderProps;
  metaProps?: MetaProps;
  page?: WordpressPage;
  post?: WordpressPost;
  statusCode?: number;
  pageTargeting?: Record<string, string | string[]>;
  disablePushPrompt?: boolean;
}

export const PageEmbeddedWidget: React.FC<{ disablePushPrompt: boolean }> = ({ disablePushPrompt }) => {
  const isDesktop = Hooks.useHydrate(checkDeviceType().isDesktop(), false);

  return (
    <>
      <Script async defer key="" src="https://accounts.google.com/gsi/client" type="text/javascript"></Script>
      {isDesktop && !disablePushPrompt && (
        <NoFirstRender>
          <PushSubscriber />
        </NoFirstRender>
      )}
    </>
  );
};

export const PaywalledMeta: React.FC<{ meta: MetaProps; canonical: string }> = ({ canonical, meta }) => {
  const paywall = useIsUserPaywalled('hard');

  return <Meta {...meta} canonical={canonical} paywall={paywall} />;
};

const Page: React.FC<PageProps> = ({
  article,
  children,
  disablePageTracking,
  disablePushPrompt = false,
  embeddedWidget,
  error,
  headerProps,
  metaProps,
  page,
  pageTargeting,
  post,
  statusCode,
}) => {
  const pathname = usePathname();
  const session = React.useContext(SessionContext);
  const authManager = session.getManager(AuthenticationManager);
  const userManager = session.getManager(UserManager);
  const [meta, setMeta] = useState<MetaProps | undefined>(metaProps);
  const [showEdgeUpsell, setShowEdgeUpsell] = useState(false);
  const isMobileHydrated = Hooks.useHydrate(isMobile(), false);

  const shouldPageTrack = statusCode !== 404 && !disablePageTracking && error !== 404;

  Hooks.useEffectDidMount(() => {
    logCommitHash();
    logReleaseVersion();
    logFriendlyBenzingaMessageToDevelopers();
  });

  useEffect(() => {
    disableOptinMonsterFonts();
    disableOptinMonsterCampaigns();
    // if (headerProps?.disableOptinMonster || authManager.isLoggedIn()) {
    //   disableOptinMonsterCampaigns();
    // }
  }, []);

  Hooks.useEffectReadyState(() => {
    if (!window.gtagAnalytics) {
      GoogleTagAnalytics.initialize({
        logger: true,
        measurementId: process.env.GOOGLE_TAG_MANAGER_MEASUREMENT_ID ?? '',
      });
    }
  }, ['interactive', 'complete']);

  useEffect(() => {
    const handlePageTransition = (event: CustomEvent) => {
      if (event?.detail?.meta) {
        setMeta(event.detail.meta);
        session.getManager(TrackingManager).setMeta(event.detail.meta);
      }
    };
    //@ts-expect-error Custom Event Type
    window.addEventListener('page-transition', handlePageTransition);
    return () => {
      //@ts-expect-error Custom Event Type
      window.removeEventListener('page-transition', handlePageTransition);
    };
  }, [session, setMeta]);

  // useScrollDepthTracking();

  const getWelcomeUrl = async (): Promise<string> => {
    const userLayoutList = await userManager.getUser()?.layout_list;
    const isUserOnBoarded = await userManager.getGlobalSettings('isUserOnBoarded', true);

    if (!isUserOnBoarded.ok && !userLayoutList?.length) {
      return `/welcome?next=${encodeURIComponent(window.location.href)}`;
    }
    return '';
  };

  const closeUpsell = async () => {
    setShowEdgeUpsell(false);
    const welcomeUrl = await getWelcomeUrl();
    if (welcomeUrl) {
      window.location.href = welcomeUrl;
    }
  };

  useEffect(() => {
    Modal.setAppElement('#__next');
    nativoAdManager.setTargeting(pageTargeting || {});
    startTransition(() => {
      raptiveAdManager.enableEnvironmentBodyClass();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    (async () => {
      const user = await userManager.getUser();
      CoralogixRumLoader(user);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOnRegister = React.useCallback(() => {
    window.location.href = '/welcome';
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const canonical = meta?.canonical ?? `https://www.benzinga.com${pathname}`;

  const navigationMenu = (getMenuByLocale(i18n.language as LocaleType, 'header') as NavigationMenus) || MainMenu;
  return (
    <>
      {!embeddedWidget && <PageEmbeddedWidget disablePushPrompt={disablePushPrompt} />}
      <NoFirstRender>
        <React.Suspense fallback={null}>
          <ClickTracker article={article} post={post || page} />
          <GeoTracker />
          <AuthPortal authMode="register" onRegister={handleOnRegister} />
          <TaboolaTrack />
        </React.Suspense>
      </NoFirstRender>
      {shouldPageTrack && meta && <PageTracker enableCoralogixRum={true} />}
      {meta && <PaywalledMeta canonical={canonical} meta={meta} />}
      {showEdgeUpsell && (
        <div className="flex flex-col max-w-[520px] size-full md:size-auto border border-black no-scrollbar overflow-scroll md:flex-row md:gap-12 fixed z-[1000003] bg-blue-950 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-6 md:p-8 md:pb-8 shadow-xl text-white backdrop-blur-sm">
          <Upsell onClose={closeUpsell} utmSource="account-creation-google-one-tap" />
        </div>
      )}
      <Schema data={formatNavigationSchema(navigationMenu.primary)} name="navigation-header" />
      {!headerProps?.hideNavigationBar && (
        <ErrorBoundary name="header">
          <NavigationHeader
            hideBanner={headerProps?.hideBanner}
            hideQuoteBar={headerProps?.hideQuoteBar}
            initialBreakingsNews={headerProps?.breakingNews}
            logoVariant={headerProps?.logoVariant ?? getLogoVariantByLocale(i18n.language as LocaleType) ?? 'default'}
            marketTickers={headerProps?.marketTickers}
            menus={navigationMenu}
            quotes={headerProps?.quotes}
            showAboveHeaderBlock={false}
            showRaptiveBanner={headerProps?.showRaptiveBanner}
            showRotatingBanner={
              typeof headerProps?.showRotatingBanner === 'boolean' ? headerProps?.showRotatingBanner : true
            }
            variant={getHeaderVariantByLocale(i18n.language as LocaleType) || 'default'}
          />
        </ErrorBoundary>
      )}
      <React.Suspense fallback={<LoadingScreen />}>{children}</React.Suspense>
      {!headerProps?.hideMobileAppBannerAd && (
        <ErrorBoundary name="mobile-app-banner-ad">
          <NoFirstRender>
            {isMobileHydrated && (
              <React.Suspense fallback={<div />}>
                <MobileAppBannerAd />
              </React.Suspense>
            )}
          </NoFirstRender>
        </ErrorBoundary>
      )}
      {!headerProps?.hideFooter && (
        <ErrorBoundary name="footer">
          <NavigationFooter
            copyrightLinks={getMenuByLocale(i18n.language as LocaleType, 'copyright') as FooterLink[]}
            logoVariant={headerProps?.logoVariant ?? getLogoVariantByLocale(i18n.language as LocaleType) ?? 'default'}
            menu={getMenuByLocale(i18n.language as LocaleType, 'footer') as FooterNavigationGroup[]}
            showStickyFooter={headerProps?.showStickyFooter}
            variant={getFooterVariantByLocale(i18n.language as LocaleType) || 'default'}
          />
        </ErrorBoundary>
      )}
    </>
  );
};

export default Page;
