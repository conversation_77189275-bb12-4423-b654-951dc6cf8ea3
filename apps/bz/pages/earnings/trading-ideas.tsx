import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import { DateTime } from 'luxon';

import styled from '@benzinga/themetron';
import {
  ContentBlock,
  ContentManager,
  PostHeader,
  WordpressSidebar,
  formatTermsSurrogateKeys,
} from '@benzinga/content-manager';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { StoryObject, combineStoryObjectArrays, filterDuplicateArticles } from '@benzinga/advanced-news-manager';
import { SafeType, safeTimeout } from '@benzinga/safe-await';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { getGlobalSession } from '../api/session';

import DefaultSidebar from '../../src/components/Sidebars/DefaultSidebar';
import { ToolsPageMain } from '../../app/tools/ToolsPageMain';
import { getTermByPath } from '../api/term';

import { Item } from '@benzinga/news';
import { PageProps, moneyMetaInfo, MoneyPageTemplate, NewsTemplate, injectBlockInLayout } from '@benzinga/money';
import { getTaboolaBlock } from '@benzinga/ads-utils';
import { termMetaInfo } from './../../app/_libs/getSlugPageData';

interface EarningsTradingIdeasPageProps extends PageProps {
  after_content?: React.ReactNode;
  news?: Item[];
  layout?: {
    campaigns?: any;
    header: PostHeader;
    above_content?: ContentBlock;
    in_content?: ContentBlock;
    below_content?: ContentBlock;
    sidebar: WordpressSidebar;
  };
}

const EarningsTradingIdeasPage: NextPage<EarningsTradingIdeasPageProps> = props => {
  const { brokerWidget, template, term } = props;

  const tabs = [
    {
      key: '/earnings',
      name: 'Overview',
    },
    {
      key: '/earnings/trading-ideas',
      name: 'Trading Ideas',
    },
  ];

  return (
    <EarningsPageContainer>
      <MoneyPageTemplate
        layoutAboveArticle={
          <NewsTemplate
            {...props}
            layoutSidebar={
              <div>
                <React.Suspense>
                  <DefaultSidebar />
                </React.Suspense>
              </div>
            }
          />
        }
        layoutAboveSidebar={false}
        layoutFooter={
          <div className="mx-4">
            <h2>Explore Benzinga&apos;s Financial Tools</h2>
            <ToolsPageMain brokerWidget={brokerWidget} />
          </div>
        }
        layoutTabs={tabs}
        layoutTabsOptions={{ prefetchAllTabs: true }}
        post={null}
        tabsTitle="Earnings"
        width="wide"
      />
    </EarningsPageContainer>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const slug = 'topic/earnings';

  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const brokerWidgetRes = await contentManager.getWordpressPost(154603);

  try {
    // Check for Money Page
    const postResponse = await contentManager.getPageWithPath(slug);
    const postData = postResponse.ok;

    if (Array.isArray(postData?.sidebar?.blocks)) {
      postData.sidebar.blocks = await loadServerSideBlockData(
        session,
        postData.sidebar.blocks,
        req.headers,
        req.cookies,
      );
    }

    // News Listing Page
    let featuredNewsResponse: SafeType<StoryObject[]> | null = null;
    let newsResponse: SafeType<StoryObject[]> | null = null;
    let featuredNews: StoryObject[] = [];

    const result = await getTermByPath(`${slug}`);

    if (!result?.ok || result?.ok?.response_code === 400) {
      res.statusCode = 404;
      return {
        props: {
          error: 404,
          featuredNews: [],
          news: [],
          topic: '',
        },
      };
    }

    const term = result?.ok?.data && result.ok.data[0];
    const metaProps = postData ? moneyMetaInfo(postData) : termMetaInfo(term);

    let template = '';

    const getTopicByNameRes = await session.getManager(ContentManager).getTopicByName(term?.name);

    if (term.vid === '3') {
      template = 'topic';
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            displayOutput: 'abstract',
            excludeAutomated: true,
            limit: 7,
            type: 'story',
          },
        ),
        3000,
      );

      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            displayOutput: 'abstract',
            headlines: undefined,
            limit: 30, //this is BAD // This parameter is not accurate often return less articles,
            type: 'story',
          },
        ),
        3000,
      );

      if (
        !getTopicByNameRes?.ok?.layout_meta?.force_index &&
        Array.isArray(newsResponse?.ok) &&
        newsResponse.ok.length < 15
      ) {
        metaProps.robots = 'noindex, nofollow';
      }
    }

    // TODO: Fetch related WP Page, use SEO Meta Data and Layout

    // const sidebarRes = await safeAwait(getMoneySidebar(96438));
    if (Array.isArray(featuredNewsResponse?.ok)) {
      featuredNews = featuredNewsResponse.ok;
    }

    let news = newsResponse?.ok ? filterDuplicateArticles(newsResponse?.ok) : [];

    const featuredNewsIds = featuredNews.map(node => node.id);

    if (7 - featuredNews.length) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
      featuredNews = featuredNews.concat(news.splice(0, 7 - featuredNews.length));
    }

    if (news.length > 0) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
    }

    if (featuredNews.length < 3) {
      news = combineStoryObjectArrays(news, featuredNews);
      featuredNews = [];
    }

    if (Array.isArray(getTopicByNameRes?.ok?.above_content?.blocks)) {
      getTopicByNameRes.ok.above_content.blocks = await loadServerSideBlockData(
        session,
        getTopicByNameRes.ok.above_content.blocks,
        req.headers,
      );
    }

    if (featuredNews[0]) {
      metaProps.dateCreated = featuredNews[0].updated;
      metaProps.dateUpdated = featuredNews[0].updated;
    }

    if (term) {
      res.setHeader('Surrogate-Key', formatTermsSurrogateKeys(term));
    }

    if (
      ['topic', 'channel'].includes(template) &&
      (featuredNewsResponse?.err || newsResponse?.err) &&
      process.env.NODE_ENV !== 'development'
    ) {
      res.statusCode = 503;
      return {
        props: {
          error: 503,
          featuredNews: [],
          news: [],
          slug,
          topic: '',
        },
      };
    }

    if (!news?.length && !getTopicByNameRes?.ok && !postData && !featuredNews?.length) {
      res.statusCode = 404;
      return {
        props: {
          error: 404,
          featuredNews: [],
          news: [],
          topic: '',
        },
      };
    }

    let layout = postData ?? getTopicByNameRes.ok ?? null;

    if (template === 'channel' || template === 'topic') {
      if (!layout) {
        layout = {
          below_main_content: null,
          header: null,
          in_content: null,
          sidebar: null,
        };
      }

      injectBlockInLayout(layout, template === 'channel' ? 'below_main_content' : 'in_content', getTaboolaBlock());
    }

    const targeting = { BZ_PTYPE: template };
    if (term) {
      targeting['BZ_CHANNEL'] = [term.name, term.tid];
    }

    return {
      props: {
        brokerWidget: brokerWidgetRes?.ok || null,
        featuredNews,
        layout,
        metaProps,
        news,
        pageTargeting: targeting,
        post: postData ?? null,
        slug,
        template: template,
        term: term,
      },
    };
  } catch (error) {
    console.error('Error with earnings/trading-ideas page data', error);
    res.statusCode = 503;
    return {
      props: {
        error: 503,
        featuredNews: [],
        news: [],
        slug,
        topic: '',
      },
    };
  }
};

export default EarningsTradingIdeasPage;

const EarningsPageContainer = styled.div``;
