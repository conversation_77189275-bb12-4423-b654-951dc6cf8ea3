const { FlatESLintConfig } = require("eslint-define-config");

module.exports = [
  {
    ignores: ["!**/*", ".next/**/*", 'public'],
  },
  {
    files: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"],
    languageOptions: {
      parser: require("@typescript-eslint/parser"),
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        project: "./tsconfig.json", // Ensure this points to your TypeScript config
      },
    },
    plugins: {
      "@nx": require("@nx/eslint-plugin"),
      "@typescript-eslint": require("@typescript-eslint/eslint-plugin"),
      "sort-destructure-keys": require("eslint-plugin-sort-destructure-keys"),
      "typescript-sort-keys": require("eslint-plugin-typescript-sort-keys"),
      "sort-keys-fix": require("eslint-plugin-sort-keys-fix"),
      "unused-imports": require("eslint-plugin-unused-imports"),
      "react-hooks": require("eslint-plugin-react-hooks"),
    },
    rules: {
      // ...require("@typescript-eslint/eslint-plugin").configs.recommended.rules,
      "react-hooks/rules-of-hooks": "error", // Checks rules of Hooks
      "react-hooks/exhaustive-deps": "warn", // Checks effect dependencies
    },
  },
  {
    plugins: {
      "@next/next": require("@next/eslint-plugin-next"),
    },
    rules: {
      "@next/next/no-html-link-for-pages": ["warn", "apps/proto/pages"],
    },
  },
  {
    files: ["**/*.spec.ts", "**/*.spec.tsx", "**/*.spec.js", "**/*.spec.jsx"],
    languageOptions: {
      globals: {
        jest: true,
      },
    },
  },
  {
    files: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"],
    languageOptions: {
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
      },
    },
    plugins: {
      "@nx": require("@nx/eslint-plugin"),
    },
    rules: {
      ...require("next").rules,
      ...require("../../.eslintrc.base.json").rules,
    },
  },
];
