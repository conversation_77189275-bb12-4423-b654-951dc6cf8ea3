'use client';
import React from 'react';
import { ArticlePageContext } from '../../context';
import { Impression } from '@benzinga/analytics';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { performRegexesOnCampaignHTML } from '@benzinga/article-manager';
import { Skeleton } from '@benzinga/core-ui';

interface Props {
  placeholderId: string;
  nodeID: string;
  strategy?: string;
  ticker: string;
  noSnippet?: boolean;
}

export const CampaignifyBlock: React.FC<Props> = ({ nodeID, noSnippet = true, placeholderId, ticker }) => {
  const [productSlug, setProductSlug] = React.useState<string>('Unknown');
  const article = React.useContext(ArticlePageContext);

  React.useEffect(() => {
    const text = article?.campaign?.[placeholderId];
    if (text) {
      const matches = text.match(/https:\/\/www.benzinga.com\/premium\/ideas\/([A-z-]+)\//);
      if (matches && matches[1]) setProductSlug(matches[1]);
    }
  }, [article, placeholderId]);

  React.useEffect(() => {
    const regExReplace = (text: string) => {
      const suppliedTickerRegEx = new RegExp(/\$\$[A-Z]{3,5}/, 'g');
      const noSuppliedTickerRegEx = new RegExp(/(\$\$)([A-Z]{3,5})/, 'g');
      const matches = text.match(/utm_source=[A-z-]+/);

      // Add campaign strategy to UTM
      if (article.campaignStrategy && matches) {
        text = text.replaceAll(matches[0], `${matches[0]}&utm_campaign=${article.campaignStrategy}`);
      }

      if (ticker) {
        const result = text?.replace(suppliedTickerRegEx, ticker);
        return result;
      } else {
        const result = text?.replace(noSuppliedTickerRegEx, '$2');
        return result;
      }
    };

    if (article?.campaign) {
      const { bottom, middle, top } = article.campaign;

      const injectCampaign = (position: string, campaign: string) => {
        if (campaign && position) {
          const campaignPlaceholder = document.querySelector(`.bz-campaign.bz-campaign-${position}-${nodeID}`);
          if (campaignPlaceholder) {
            campaignPlaceholder.innerHTML = sanitizeHTML(performRegexesOnCampaignHTML(campaign));
            const anchorTag = campaignPlaceholder.querySelector('a');
            if (anchorTag) {
              anchorTag.target = '_blank';
              anchorTag.setAttribute('data-action', 'In Article Campaignify Link Click');
            }
          }
        }
      };

      if (top) {
        injectCampaign('top', regExReplace(top));
      }

      if (middle) {
        injectCampaign('middle', regExReplace(middle));
        injectCampaign('middle-2', regExReplace(middle));
      }

      if (bottom) {
        injectCampaign('bottom', regExReplace(bottom));
        injectCampaign('bottom-2', regExReplace(bottom));
      }
    }
  }, [article.campaignStrategy, article.campaign, ticker, nodeID]);

  if (article.hide || article.campaign === null) return null;

  return (
    <Impression campaign_id={`${placeholderId} ${nodeID}`} tag={productSlug} unit_type="Campaignify Unit">
      <div className={`bz-campaign bz-campaign-${placeholderId}-${nodeID}`} data-nosnippet={noSnippet ? true : null}>
        <Skeleton active paragraph={{ rows: 7 }} />
        <div className="flex sm:hidden flex-col">
          <Skeleton active paragraph={{ rows: 4 }} />
        </div>
      </div>
    </Impression>
  );
};
