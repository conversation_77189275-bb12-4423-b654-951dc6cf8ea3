'use client';
import React from 'react';
import styled from 'styled-components';
interface Props {
  hideSearchTips?: boolean;
  message?: React.ReactNode;
  style?: React.CSSProperties;
}

export const NoResults: React.FC<Props> = ({ hideSearchTips, message, style }) => {
  return (
    <NoResultsContainer style={style}>
      <NoResultsTitle>{message ?? 'No results found.'}</NoResultsTitle>
      {!hideSearchTips && (
        <Tips>
          <Header>Search Tips:</Header>
          <Ul>
            <Li>Make your search as concise as possible.</Li>
            <Li>Ensure words are spelled correctly.</Li>
            <Li>Try less specific keywords.</Li>
          </Ul>
        </Tips>
      )}
    </NoResultsContainer>
  );
};

const NoResultsContainer = styled.div`
  font-size: 15px;
  margin-left: 3em;
  margin-top: 2em;
  text-align: left;
`;

const NoResultsTitle = styled.div`
  font-size: 1.25em;
  font-weight: lighter;
  padding-bottom: 10px;
  color: ${props => props.theme.colors.foregroundInactive};
`;
const Tips = styled.div`
  font-size: 0.9em;
  color: ${props => props.theme.colors.foregroundInactive};
  margin-top: 1em;
`;

const Header = styled.div`
  font-size: 1em;
`;

const Ul = styled.ul`
  margin-top: 0.5em;
`;

const Li = styled.li`
  margin-top: 0.25em;
`;
