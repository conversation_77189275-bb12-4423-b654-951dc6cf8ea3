import { ColDef, ValueFormatterParams, ValueGetterParams } from '@ag-grid-community/core';
import { TimeFormat } from '@benzinga/date-utils';
import { DateTime } from 'luxon';
import { NO_VALUE } from '../../../Ticker';

const checkDateTime = (valueA: string, valueB: string): number => {
  const dateTimeA = DateTime.fromJSDate(new Date(valueA));
  const dateTimeB = DateTime.fromJSDate(new Date(valueB));

  if (!dateTimeA.isValid || !dateTimeB.isValid) {
    return 0;
  }

  if (dateTimeA < dateTimeB) {
    return -1;
  } else if (dateTimeA > dateTimeB) {
    return 1;
  }
  return 0;
};

export interface DateColDefTimeSettings {
  timeFormat: TimeFormat;
  timeOffset: number;
}

export const DateColDef = (colDef: ColDef, { timeOffset }: DateColDefTimeSettings): ColDef => ({
  cellStyle: { 'text-align': 'left' },
  comparator: checkDateTime,
  filter: 'agDateColumnFilter',
  filterParams: {
    comparator: checkDateTime,
  },
  filterValueGetter: ({ colDef: { field = 'time' }, data }: ValueGetterParams) => {
    const value = data[field];

    return DateTime.fromISO(value).toJSDate();
  },
  valueFormatter: ({ value }: ValueFormatterParams): string => (value !== 0 ? value : NO_VALUE),
  valueGetter: ({ colDef: { field = 'time' }, data }: ValueGetterParams) => {
    const value = data[field];
    const hasTime = typeof value === 'string' && value.includes('T');

    let time = value;
    if (typeof value === 'string' && hasTime) {
      const dateTime = DateTime.fromISO(value);
      time = dateTime.isValid ? dateTime.plus({ minutes: timeOffset }).toFormat('yyyy-MM-dd HH:mm:ss') : value;
    } else if (typeof value === 'string') {
      const dateTime = DateTime.fromISO(value);
      time = dateTime.isValid ? dateTime.toFormat('yyyy-MM-dd') : value;
    }

    return time !== 'Invalid DateTime' ? time : value;
  },
  ...colDef,
});
