'use client';
import React, { useEffect, useCallback } from 'react';
import { Icon, Button, NoResults, Pagination } from '@benzinga/core-ui';
import { faSlidersH } from '@fortawesome/pro-light-svg-icons/faSlidersH';
import styled from '@benzinga/themetron';
import {
  AltOffering,
  FilterOptions as IFilterOptions,
  FilterOptionsSortBy,
  AltOfferingsManager,
} from '@benzinga/alts-manager';
import { SessionContext } from '@benzinga/session-context';
import { faRedo } from '@fortawesome/pro-regular-svg-icons/faRedo';
import { CompareProductTable, CompareProductTableProps } from '@benzinga/product-ui';
import { Product } from '@benzinga/content-manager';
import { OfferingsFilter } from './OfferingsFilter';
import { OfferingsGrid } from './OfferingsGrid';
import { useFilters } from '@benzinga/filter-ui';
import { debounce } from 'lodash';

type FilterOptions = IFilterOptions & { sortBy?: FilterOptionsSortBy };
export interface OfferingsProps {
  allOfferings: AltOffering[];
  allowFilter?: boolean;
  allowSort?: boolean;
  handleLeadCapture?: () => void;
  hideTargetReturn?: boolean;
  hidePropertyType?: boolean;
  hideMinInvestment?: boolean;
  hideRating?: boolean;
  hideInvestorType?: boolean;
  renderCompareProduct?: boolean;
  initialFilters?: FilterOptions;
  total?: number;
  availableVerticals?: any;
}

export const Offerings: React.FC<OfferingsProps> = ({
  allOfferings,
  allowFilter = true,
  allowSort = true,
  availableVerticals = [],
  handleLeadCapture,
  hideInvestorType = false,
  hideMinInvestment = false,
  hidePropertyType = false,
  hideRating = false,
  hideTargetReturn = false,
  initialFilters = {} as FilterOptions,
  renderCompareProduct = false,
  total = 0,
}) => {
  const initialFilterOptions: FilterOptions = React.useMemo(() => {
    return {
      categoryTypes: initialFilters.categoryTypes ?? [],
      investorType: initialFilters.investorType ?? 'All',
      minimumInvestment: initialFilters.minimumInvestment ?? [0, 250000],
      page: initialFilters.page ?? 1,
      propertyTypes: initialFilters.propertyTypes ?? [],
      rating: initialFilters.rating ?? '0,5',
      sortBy: initialFilters.sortBy ?? ('Recommended' as FilterOptionsSortBy),
      targetReturn: initialFilters.targetReturn ?? [],
      textSearch: initialFilters.textSearch ?? '',
      verticalTypes: initialFilters.verticalTypes ?? '',
    };
  }, [
    initialFilters.categoryTypes,
    initialFilters.investorType,
    initialFilters.minimumInvestment,
    initialFilters.propertyTypes,
    initialFilters.rating,
    initialFilters.sortBy,
    initialFilters.targetReturn,
    initialFilters.textSearch,
    initialFilters.verticalTypes,
    initialFilters.page,
  ]);

  const prepareCompareOfferings = offererings => {
    const tempProducts: Product[] = [];
    offererings?.map((offer: AltOffering, index: number) => {
      if (offer?.productBlock?.products[0]) {
        tempProducts[index] = offer.productBlock.products[0];
      }
    });
    return tempProducts;
  };

  const [filtersData, updateFiltersData] = useFilters<FilterOptions>(initialFilterOptions);
  const [currentPage, setCurrentPage] = React.useState(initialFilters.page || 1);

  const [totalOfferings, setTotalOfferings] = React.useState<number>(total || 100);

  const [isFilterOpen, setIsFilterOpen] = React.useState(false);
  const [offerings, setOfferings] = React.useState<AltOffering[]>(allOfferings);

  const [offeringsCompare, setOfferingsCompare] = React.useState<CompareProductTableProps>({
    products: prepareCompareOfferings(allOfferings) ?? [],
  });

  const [textSearch, setTextSearch] = React.useState(filtersData?.textSearch || '');
  const [minimumInvestment, setMinimumInvestment] = React.useState<[number, number]>(
    filtersData?.minimumInvestment || [0, 250000],
  );
  const [rating, setRating] = React.useState<string>(filtersData?.rating || '0,5');
  const [investorType, setInvestorType] = React.useState(filtersData?.investorType || 'All');
  const [propertyTypes, setPropertyTypes] = React.useState<string[]>(filtersData?.propertyTypes || []);
  const [availablePropertyTypes, setAvailablePropertyTypes] = React.useState<string[]>([]);
  const [categories, setCategories] = React.useState<string[]>([]);
  const [verticals, setVerticals] = React.useState<string[]>([]);
  const [categoryTypes, setCategoryTypes] = React.useState<string[]>(filtersData?.categoryTypes || []);
  const [verticalTypes, setVerticalTypes] = React.useState<string[]>([]);
  const [targetReturn, setTargetReturn] = React.useState<string[]>(filtersData?.targetReturn || []);
  const [sortBy, setSortBy] = React.useState<FilterOptionsSortBy>(filtersData.sortBy);

  const session = React.useContext(SessionContext);
  const OfferingsCalendarManager = session.getManager(AltOfferingsManager);
  OfferingsCalendarManager.addOfferings(allOfferings);

  const filterOptions = React.useMemo(() => {
    return { categoryTypes, investorType, minimumInvestment, propertyTypes, sortBy, targetReturn, textSearch };
  }, [categoryTypes, investorType, minimumInvestment, propertyTypes, sortBy, targetReturn, textSearch]);

  const [initialQueried, setInitialQueried] = React.useState<boolean>(false);
  useEffect(() => {
    const findCategories = result => {
      const _types: string[] = result
        .filter(function (offering) {
          if (offering.type != '') return offering.type;
        })
        .map(function (offering) {
          return offering.type;
        });
      const _categories = Array.from(new Set(_types));
      setCategories(_categories);
    };

    const findAvailablePropertyTypes = result => {
      const _availablePropertyTypes: string[] = result
        .filter(function (offering) {
          if (offering.propertyType != '' && offering.propertyType != null) return offering.type;
        })
        .map(offering => offering.propertyType);
      const formattedPropertyTypes: string[] = Array.from(new Set(_availablePropertyTypes));
      setAvailablePropertyTypes(formattedPropertyTypes);
    };

    if (allOfferings) {
      findAvailablePropertyTypes(allOfferings);
      findCategories(allOfferings);
    }
  }, [allOfferings]);

  useEffect(
    () => {
      if (renderCompareProduct) {
        setTotalOfferings(total);
        const _verticals: string[] = [];
        availableVerticals.forEach((vertical: any) => {
          if (vertical.parent == 0) {
            _verticals[vertical.term_id] = vertical?.term?.name;
          }
        });

        setVerticals(_verticals);
        if (initialFilters?.verticalTypes && initialFilters?.verticalTypes != '') {
          setVerticalTypes(initialFilters?.verticalTypes.split(','));
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const handleFilter = React.useCallback(
    async (filterOptions: FilterOptions, updateFilters = true) => {
      const initialInvestorType = initialFilterOptions.investorType;

      if (updateFilters) {
        updateFiltersData(filterOptions);
      }

      const newInvestorType = filterOptions.investorType ? filterOptions.investorType : initialInvestorType;
      const newPropertyType =
        filterOptions.propertyTypes?.length === 0 ? availablePropertyTypes : filterOptions.propertyTypes;
      const newTargetReturn = filterOptions.targetReturn?.length === 0 ? targetReturn : filterOptions.targetReturn;
      const newMinimumInvestment = filterOptions.minimumInvestment;
      const newRating = filterOptions.rating ?? '0,5';
      const newCategoryType = filterOptions.categoryTypes?.length === 0 ? categories : filterOptions.categoryTypes;
      const newVerticalTypes = filterOptions.verticalTypes ?? '';
      const newSortBy = filterOptions.sortBy;
      const newTextSearch = filterOptions.textSearch;
      const page = filterOptions.page ?? 1;

      const filters = {
        categoryTypes: newCategoryType,
        investorType: newInvestorType,
        minimumInvestment: newMinimumInvestment,
        page: page,
        propertyTypes: newPropertyType,
        rating: newRating,
        searchType: renderCompareProduct ? 'review' : '',
        sortBy: newSortBy,
        targetReturn: newTargetReturn,
        textSearch: newTextSearch,
        verticalTypes: newVerticalTypes,
      };

      if (renderCompareProduct) {
        if (initialQueried) {
          debounceReviewSearch(filters);
        } else {
          updateOfferingCompare(allOfferings);
        }
      } else {
        const filteredOfferings = await OfferingsCalendarManager.filterOfferings(filters);

        if (filteredOfferings && filteredOfferings.ok) {
          setOfferings(filteredOfferings.ok);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [initialFilterOptions.investorType, availablePropertyTypes, targetReturn, categories, OfferingsCalendarManager],
  );

  const debounceReviewSearch = React.useRef(
    debounce(async filters => {
      const filteredReviews = await OfferingsCalendarManager.filterReviewOfferings(filters);
      if (filteredReviews.ok?.offerings) {
        setOfferings(filteredReviews.ok.offerings);
        updateOfferingCompare(filteredReviews.ok.offerings);
        setTotalOfferings(filteredReviews.ok?.total);
      }
    }, 300),
  ).current;

  const updateOfferingCompare = React.useCallback(
    offeringsData => {
      const tempProducts: Product[] = prepareCompareOfferings(offeringsData);
      setOfferingsCompare({
        ...offeringsCompare,
        products: tempProducts,
      });
    },
    [offeringsCompare],
  );

  useEffect(() => {
    if (!initialQueried) {
      handleFilter({ ...filterOptions, sortBy }, false);
      setInitialQueried(true);
    }
  }, [filterOptions, handleFilter, sortBy, initialQueried]);

  const handleChangeSortBy = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as FilterOptionsSortBy;
    handleFilter({ ...filterOptions, sortBy: value });
    setSortBy(value);
  };

  const handleChangeTextSearch = React.useCallback(
    (value: string) => {
      setCurrentPage(1);
      handleFilter({ ...filterOptions, page: 1, textSearch: value.toLowerCase() });
      setTextSearch(value);
    },
    [handleFilter, filterOptions],
  );

  const handleChangeMinimumInvestment = React.useCallback(
    (value: [number, number]) => {
      handleFilter({ ...filterOptions, minimumInvestment: value });
      setMinimumInvestment(value);
    },
    [handleFilter, filterOptions],
  );

  const handleChangeRating = React.useCallback(
    (value: [number, number]) => {
      setCurrentPage(1);
      const ratingString = value?.join(',');
      handleFilter({ ...filterOptions, page: 1, rating: ratingString });
      setRating(ratingString);
    },
    [handleFilter, filterOptions],
  );

  const handleChangeInvestorType = React.useCallback(
    (value: string) => {
      handleFilter({ ...filterOptions, investorType: value });
      setInvestorType(value);
    },
    [handleFilter, filterOptions],
  );

  const handleChangePropertyType = React.useCallback(
    (value: string[]) => {
      handleFilter({ ...filterOptions, propertyTypes: value });
      setPropertyTypes(value);
    },
    [handleFilter, filterOptions],
  );

  const handleChangeCategoryType = React.useCallback(
    (value: string[]) => {
      handleFilter({ ...filterOptions, categoryTypes: value });
      setCategoryTypes(value);
    },
    [handleFilter, filterOptions],
  );

  const handleChangeVerticalType = React.useCallback(
    (value: string[]) => {
      setCurrentPage(1);
      handleFilter({ ...filterOptions, page: 1, verticalTypes: value?.join(',') });
      setVerticalTypes(value);
    },
    [handleFilter, filterOptions],
  );

  const handleChangeTargetReturn = React.useCallback(
    (value: string[]) => {
      handleFilter({ ...filterOptions, targetReturn: value });
      setTargetReturn(value);
    },
    [handleFilter, filterOptions],
  );

  const handleReset = useCallback(() => {
    setCategoryTypes([]);
    setVerticalTypes([]);
    setPropertyTypes([]);
    setTargetReturn([]);
    setInvestorType('All');
    setMinimumInvestment([0, 250000]);
    setTextSearch('');
    setCurrentPage(1);
    setRating('0,5');
    handleFilter({ ...initialFilterOptions, page: 1 });
  }, [handleFilter, initialFilterOptions]);

  const handleToggleFilter = () => setIsFilterOpen(!isFilterOpen);

  const handleClickTag = useCallback(
    (label, value) => {
      handleReset();
      if (label === 'investorType') {
        handleFilter({ ...initialFilterOptions, investorType: value });
        setInvestorType(value);
      } else if (label === 'propertyType') {
        handleFilter({ ...initialFilterOptions, propertyTypes: value });
        setPropertyTypes(value);
      } else if (label === 'category') {
        handleFilter({ ...initialFilterOptions, categoryTypes: value });
        setCategoryTypes(value);
      }

      const exploreOfferingsSection = document.getElementById('explore-offerings-section');
      if (exploreOfferingsSection) {
        exploreOfferingsSection.scrollIntoView({ behavior: 'smooth' });
      }
    },
    [handleFilter, handleReset, initialFilterOptions],
  );

  const handleOnChangePagination = React.useCallback(
    data => {
      const queryPage = data.nextPage;
      setCurrentPage(queryPage);
      handleFilter({ ...filterOptions, page: data.nextPage });
    },
    [handleFilter, filterOptions],
  );

  return (
    <OfferingsContainer className={`offerings-container ${isFilterOpen ? 'filter-open' : ''}`}>
      {allowFilter && (
        <OfferingsFilter
          availablePropertyTypes={availablePropertyTypes}
          categories={categories}
          categoryTypes={categoryTypes}
          hideInvestorType={hideInvestorType}
          hideMinInvestment={hideMinInvestment}
          hidePropertyType={hidePropertyType}
          hideRating={hideRating}
          hideTargetReturn={hideTargetReturn}
          investorType={investorType}
          minimumInvestment={minimumInvestment}
          offerings={offerings}
          onChangeCategoryType={handleChangeCategoryType}
          onChangeInvestorType={handleChangeInvestorType}
          onChangeMinimumInvestment={handleChangeMinimumInvestment}
          onChangePropertyType={handleChangePropertyType}
          onChangeRating={handleChangeRating}
          onChangeTargetReturn={handleChangeTargetReturn}
          onChangeTextSearch={handleChangeTextSearch}
          onChangeVerticalType={handleChangeVerticalType}
          onReset={handleReset}
          onToggleFilter={handleToggleFilter}
          propertyTypes={propertyTypes}
          rating={rating}
          targetReturns={targetReturn}
          textSearch={textSearch}
          verticals={verticals}
          verticalTypes={verticalTypes}
        />
      )}

      <div className="filter-button-sort-container">
        {allowSort && (
          <SortSelect handleChangeSortBy={handleChangeSortBy} initialFilterOptions={filtersData as FilterOptions} />
        )}
        {allowFilter && (
          <Button className="toggle-filter-button" onClick={handleToggleFilter} variant="primary">
            <span>Filters</span>
            <Icon className="slider-icon" icon={faSlidersH} />
          </Button>
        )}
      </div>

      {offerings?.length > 0 ? (
        <div className="offerings-container">
          {allowSort && (
            <SortSelect
              className="desktop"
              handleChangeSortBy={handleChangeSortBy}
              initialFilterOptions={filtersData as FilterOptions}
            />
          )}
          {renderCompareProduct ? (
            <>
              <CompareProductTable products={offeringsCompare.products} />
              {totalOfferings > 0 && offerings?.length > 0 && (
                <div className="pagination-wrapper">
                  <Pagination
                    buttonClassName="pagination-button"
                    defaultPage={currentPage}
                    disabledPrevNextButton
                    onPageChanged={handleOnChangePagination}
                    pageNeighbors={1}
                    pageSize={offeringsCompare?.products.length}
                    totalItems={Number(totalOfferings) || 100}
                  />
                </div>
              )}
            </>
          ) : (
            <OfferingsGrid handleLeadCapture={handleLeadCapture} offerings={offerings} onTagClick={handleClickTag} />
          )}
        </div>
      ) : (
        <NoResults className="no-matching-listings" title="There are no listings matching your search">
          <button className="reset-filters-button" onClick={handleReset}>
            <Icon icon={faRedo} />
            Reset Filters
          </button>
        </NoResults>
      )}
    </OfferingsContainer>
  );
};

const SortSelect: React.FC<{
  className?: string;
  handleChangeSortBy: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  initialFilterOptions: FilterOptions;
}> = ({ className = '', handleChangeSortBy, initialFilterOptions }) => {
  return (
    <div className={`sort-select ${className}`}>
      <select defaultValue={initialFilterOptions.sortBy} onChange={handleChangeSortBy}>
        <option value="Recommended">Featured</option>
        <option value="Newest">Newest</option>
        <option value="Highest Yield">Yield: High to Low</option>
        <option value="Lowest Yield">Yield: Low to High</option>
        <option value="Lowest Minimum Investment">Minimum Investment: Low to High</option>
        <option value="Highest Minimum Investment">Minimum Investment: High to Low</option>
      </select>
    </div>
  );
};

const OfferingsContainer = styled.div`
  &.offerings-container {
    display: flex;
    flex-direction: column;
    position: relative;

    .filter-button-sort-container {
      display: flex;
    }

    .toggle-filter-button {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      width: 100%;
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
      text-transform: uppercase;
      margin-bottom: 10px;

      > span {
        display: contents;
      }

      .slider-icon {
        color: ${({ theme }) => theme.colorPalette.blue500};
        font-size: ${({ theme }) => theme.fontSize.xl};
        margin-left: 10px;
      }
    }

    .sort-select {
      width: 100%;
      margin-right: 0.5rem;
      height: 40px;

      select {
        width: 100%;
        height: 100%;
        border-radius: ${({ theme }) => theme.borderRadius.default};
        border: 1px solid ${({ theme }) => theme.colorPalette.gray100};
      }

      &.desktop {
        display: none;
      }
    }

    @media screen and (min-width: 790px) {
      flex-direction: row;

      .filter-button-sort-container {
        display: none;
      }
      .sort-select {
        width: auto;
        margin-bottom: 1rem;
        margin-right: 0;

        select {
          width: auto;
          padding: 0px 12px;
        }

        &.desktop {
          display: block;
        }
      }
    }

    &.filter-open {
      margin-top: 15px;
      .offerings-filter-container {
        display: flex;
        top: 50px;
      }
    }

    .offerings-filter-container {
      position: absolute;
      top: 15px;
      left: 0;
      width: 100%;
      background-color: #ffffff;
      justify-content: center;
      display: none;
      z-index: 1;

      @media screen and (min-width: 820px) {
        width: 200px;
        min-width: 200px;
        height: 100%;
        display: flex;
        position: sticky;
      }

      @media screen and (min-width: 1100px) {
        width: 250px;
        min-width: 250px;
        height: 100%;
      }
    }

    .no-matching-listings {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;
      min-height: 60vh;
      width: 100%;

      .frown-icon {
        font-size: ${({ theme }) => theme.fontSize['5xl']};
        margin-bottom: 10px;
        color: #c4c4c4;
      }

      p {
        font-size: ${({ theme }) => theme.fontSize.base};
        margin-bottom: 0;
      }

      .reset-filters-button {
        display: flex;
        align-items: center;
        justify-content: center;
        color: ${({ theme }) => theme.colorPalette.white};
        font-size: ${({ theme }) => theme.fontSize.base};
        background: ${({ theme }) => theme.colorPalette.blue500};
        border-radius: ${({ theme }) => theme.borderRadius.sm};
        margin-top: 10px;
        padding: 5px 10px;

        &:hover {
          background: ${({ theme }) => theme.colorPalette.blue400};
        }

        > span {
          transform: rotate(270deg);
          margin-right: 5px;
        }
      }
    }

    @media screen and (min-width: 820px) {
      .offerings-container {
        // max-width: 700px;
        width: 100%;
      }
    }
  }
`;
