import React from 'react';
import styled from '@benzinga/themetron';
import { faRedo } from '@fortawesome/pro-regular-svg-icons/faRedo';
import { AltOffering } from '@benzinga/alts-manager';
import { CheckboxGroup, Input, Slider, Tabs, Icon } from '@benzinga/core-ui';

export interface OfferingsFilterProps {
  availablePropertyTypes: string[];
  categories: string[];
  categoryTypes: string[];
  hideTargetReturn?: boolean;
  hidePropertyType?: boolean;
  hideRating?: boolean;
  hideMinInvestment?: boolean;
  hideInvestorType?: boolean;
  onChangeCategoryType: (value: string[]) => void;
  onChangeVerticalType: (value: string[]) => void;
  onChangeInvestorType: (value: string) => void;
  onChangeMinimumInvestment: (value: [number, number]) => void;
  onChangePropertyType: (value: string[]) => void;
  onChangeRating: (value: [number, number]) => void;
  onChangeTextSearch: (value: string) => void;
  onChangeTargetReturn: (value: string[]) => void;
  targetReturns: string[];
  onToggleFilter?: () => void;
  onReset: () => void;
  investorType: string;
  minimumInvestment: [number, number] | null;
  offerings: AltOffering[];
  propertyTypes: string[];
  rating: string;
  textSearch: string;
  verticals?: string[];
  verticalTypes?: string[];
}

const getRatingRange = rating => {
  return rating && !Array.isArray(rating)
    ? rating.split(',').map(function (x) {
        return parseInt(x);
      })
    : [0, 5];
};

export const OfferingsFilter: React.FC<OfferingsFilterProps> = ({
  availablePropertyTypes,
  categories,
  categoryTypes,
  hideInvestorType = false,
  hideMinInvestment = false,
  hidePropertyType,
  hideRating = false,
  hideTargetReturn,
  investorType,
  minimumInvestment,
  onChangeCategoryType,
  onChangeInvestorType,
  onChangeMinimumInvestment,
  onChangePropertyType,
  onChangeRating,
  onChangeTargetReturn,
  onChangeTextSearch,
  onChangeVerticalType,
  onReset,
  onToggleFilter,
  propertyTypes,
  rating,
  targetReturns,
  textSearch,
  verticals = [],
  verticalTypes = [],
}) => {
  const ratingRange: number[] = getRatingRange(rating);
  const availableTargetReturns = ['<5%', '5-10%', '10-15%', '15-20%', '20%+'];

  return (
    <OfferingsFilterContainer className="offerings-filter-container">
      <div>
        <div className="input-group">
          <label>Search</label>
          <Input onChange={onChangeTextSearch} value={textSearch} />
        </div>

        {!hideTargetReturn && availableTargetReturns && availableTargetReturns.length > 0 && (
          <div className="input-group">
            <label>Target Return</label>
            <CheckboxGroup onChange={onChangeTargetReturn} options={availableTargetReturns} selected={targetReturns} />
          </div>
        )}

        {!hideMinInvestment && minimumInvestment && (
          <div className="input-group">
            <label>Minimum Investment</label>
            <div className="slider-container">
              <span>
                ${minimumInvestment[0]} – ${minimumInvestment[1]}
              </span>
              <Slider
                defaultValue={minimumInvestment}
                max={250000}
                min={0}
                multi={true}
                onChange={onChangeMinimumInvestment as ((value: number | [number, number]) => void) | undefined}
                step={1000}
              />
            </div>
          </div>
        )}

        {!hideInvestorType && (
          <div className="input-group">
            <label>Investor Type</label>
            <Tabs
              activeTab={investorType}
              isShowMoreButtonEnabled={false}
              onChange={onChangeInvestorType}
              tabs={[
                {
                  key: 'All',
                  name: 'All',
                },
                {
                  key: 'Accredited',
                  name: 'Accredited',
                },
                {
                  key: 'Non-Accredited',
                  name: 'Non-Accredited',
                },
              ]}
            />
          </div>
        )}

        {!hideRating && rating && ratingRange && (
          <div className="input-group">
            <label>Rating</label>
            <div className="slider-container">
              <span>
                {ratingRange[0]} – {ratingRange[1]}
              </span>
              <Slider
                defaultValue={ratingRange}
                max={5}
                min={0}
                multi={true}
                onChange={onChangeRating as ((value: number | [number, number]) => void) | undefined}
                step={1}
              />
            </div>
          </div>
        )}

        {categories && categories.length > 0 && (
          <div className="input-group">
            <label>Categories</label>
            <CheckboxGroup onChange={onChangeCategoryType} options={categories} selected={categoryTypes} />
          </div>
        )}

        {!hidePropertyType && availablePropertyTypes && availablePropertyTypes.length > 0 && (
          <div className="input-group">
            <label>Available Property Types</label>
            <CheckboxGroup onChange={onChangePropertyType} options={availablePropertyTypes} selected={propertyTypes} />
          </div>
        )}

        {!!verticals?.length && (
          <div className="input-group">
            <label>Verticals</label>
            <CheckboxGroup onChange={onChangeVerticalType} options={verticals} selected={verticalTypes} />
          </div>
        )}
      </div>
      <div className="buttons-group">
        <div className="search-button-container">
          <button className="reset-filters-button mobile" onClick={onReset}>
            <Icon icon={faRedo} />
          </button>
          <button className="close-button" onClick={onToggleFilter}>
            ×
          </button>
        </div>
        <button className="reset-filters-button" onClick={onReset}>
          <Icon icon={faRedo} />
          Reset Filters
        </button>
      </div>
    </OfferingsFilterContainer>
  );
};

const OfferingsFilterContainer = styled.div`
  &.offerings-filter-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    border: 1px solid ${({ theme }) => theme.colors.border};
    padding: 10px;

    .tabs-container .tab-list {
      margin: 0;
      overflow-x: auto;
      border: solid 1px #ddd;

      button.tab {
        display: flex;
        align-items: center;
        width: 100%;
        height: 50px;
        justify-content: center;
        background-color: #ffffff;
        color: #000000;
        border-right: solid 1px #ddd;
        margin-right: 0;
        font-size: ${({ theme }) => theme.fontSize.sm};
        font-weight: ${({ theme }) => theme.fontWeight.normal};
        padding: 8px;

        &.active {
          background-color: ${({ theme }) => theme.colorPalette.blue500};
          color: #ffffff;

          &:hover {
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            color: #ffffff;
          }
        }

        &:nth-of-type(1) {
          border-top-left-radius: ${({ theme }) => theme.borderRadius.sm};
          border-bottom-left-radius: ${({ theme }) => theme.borderRadius.sm};
        }

        &:nth-of-type(2) {
          border-top-right-radius: ${({ theme }) => theme.borderRadius.sm};
          border-bottom-right-radius: ${({ theme }) => theme.borderRadius.sm};
        }

        &:last-of-type {
          border-right: none;
        }

        &.selected {
          background-color: #e8743e;
          color: #ffffff;

          > span {
            color: #ffffff;
          }
        }

        > span {
          margin-right: 5px;
          font-size: ${({ theme }) => theme.fontSize.xl};
        }

        @media screen and (min-width: 800px) {
          background-color: transparent;
          height: auto;

          &:nth-of-type(1),
          &:nth-of-type(2) {
            border-radius: none;
          }

          &.selected {
            background-color: transparent;
            color: #242429;

            > span {
              color: ${({ theme }) => theme.colorPalette.blue500};
            }
          }
        }
      }
    }

    .input-group {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;

      > label {
        font-size: ${({ theme }) => theme.fontSize.base};
        margin-bottom: 5px;
      }

      input[type='text'] {
        font-size: 15px;
        padding: 0.25rem 0.5rem;
      }
    }

    .slider-container {
      > span {
        font-weight: ${({ theme }) => theme.fontWeight.semibold};
        font-size: ${({ theme }) => theme.fontSize.base};
      }
    }

    .buttons-group {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 10px;

      button {
        display: flex;
        align-items: center;
        justify-content: center;

        &.reset-filters-button {
          display: none;
          color: ${({ theme }) => theme.colorPalette.gray600};

          &.mobile {
            display: flex;
            background-color: #e5e5e5;
            border-radius: ${({ theme }) => theme.borderRadius.sm};
            height: 45px;
            margin-left: 10px;
            width: 50%;
          }

          > span {
            transform: rotate(270deg);
          }

          @media screen and (min-width: 800px) {
            display: flex;
            margin-top: 10px;

            > span {
              margin-right: 10px;
            }

            &.mobile {
              display: none;
            }
          }
        }
      }
    }

    .search-button-container {
      display: flex;
      width: 100%;

      .close-button {
        background-color: #e5e5e5;
        height: 45px;
        font-size: 1.4rem;
        margin-left: 10px;
        border-radius: ${({ theme }) => theme.borderRadius.sm};
        width: 50%;

        @media screen and (min-width: 800px) {
          display: none;
        }
      }
    }
  }
`;
