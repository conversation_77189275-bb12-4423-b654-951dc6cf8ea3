'use client';
import React from 'react';
import { GetServerSideProps } from 'next';

import { Checkbox } from '@benzinga/core-ui';
import { handleSorting } from './utils';
import { getSessionSingleton } from '@benzinga/session';

import { ColumnType } from 'antd/lib/table';
import { Tooltip, Icon } from '@benzinga/core-ui';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons/faInfoCircle';

import { customColumnSettings, customMobileColumnSettings } from './columnSettings';
import { Product, ProductDataAttribute, ContentManager } from '@benzinga/content-manager';
import { Filter, Filters, matchFilterAttributes, Range, MultiRange, RangeSelectFilter } from '@benzinga/filter-ui';
import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import { runningClientSide } from '@benzinga/utils';

const Table = React.lazy(() =>
  import('antd').then(module => ({
    default: module.Table,
  })),
);

const AnnualYieldDefault = [0.5, 15] as Range as MultiRange;
const RiskLvlDefault = [1, 5] as Range as MultiRange;

const riskFilter = {
  key: 'yield_investments_risk_level',
  label: 'Risk Level',
  range: RiskLvlDefault,
  rangeLabels: ['Safest', 'High Risk'],
  selected: RiskLvlDefault,
  step: 1,
  type: 'range-select',
};

const lowRiskFilter = {
  ...riskFilter,
  range: [1, 3],
  rangeLabels: ['Safest', 'Some Risk'],
  selected: [1, 3],
};

const mobileProductFilters: Filter[] = [
  {
    key: 'yield',
    label: 'Annual Yield',
    range: AnnualYieldDefault,
    selected: AnnualYieldDefault,
    step: 0.5,
    type: 'range-select',
    unit: '%',
  } as RangeSelectFilter,
];

export type YieldTableProps = {
  products: Product[];
  productFilters?: Filter[];
};

export const YieldTable: React.FC<YieldTableProps> = ({ productFilters = [], products }) => {
  const { width } = Hooks.useWindowSize();
  const [filters, setFilters] = React.useState<Filter[]>(productFilters ?? []);
  const [selectedFilters, setSelectedFilters] = React.useState<Set<string>>(new Set());

  const [lowRisk, setLowRisk] = React.useState<boolean>(false);

  React.useEffect(() => {
    setFilters(width > 660 ? productFilters : mobileProductFilters);
  }, [width, productFilters]);

  const handleFilterChange = React.useCallback(
    (filter: Filter) => {
      const updatedFilters = filters.map(f => f);
      setFilters(updatedFilters);

      const selected = new Set([...selectedFilters]);

      if (Array.isArray(filter.selected) && filter.selected.length) {
        selected.add(filter.key);
      } else if (!Array.isArray(filter.selected) && filter.selected) {
        selected.add(filter.key);
      } else {
        selected.delete(filter.key);
      }

      setSelectedFilters(selected);
    },
    [filters, selectedFilters],
  );

  const handleSignificantRisk = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event) {
      const isChecked = event.target?.checked;
      setLowRisk(isChecked);
      const targetFilter = isChecked ? (lowRiskFilter as RangeSelectFilter) : (riskFilter as RangeSelectFilter);

      handleFilterChange(targetFilter);
      setFilters(filters =>
        filters.map((item: Filter): Filter => {
          if (item.key === 'yield_investments_risk_level') {
            return targetFilter;
          }

          return item;
        }),
      );
    }
  };

  const filteredProducts = React.useCallback((): Product[] => {
    if (!Array.isArray(products)) {
      return [];
    }

    if (!selectedFilters?.size) {
      return products;
    }

    const filtersCollection = [...filters] as Filter[];
    // Hack to handle lowrisk on mobile
    if (lowRisk) {
      filtersCollection.push(lowRiskFilter as Filter);
    }

    return products.filter(product => {
      return product.data ? matchFilterAttributes(filtersCollection, selectedFilters, product.data) : false;
    });
  }, [filters, selectedFilters, products, lowRisk]);

  const formatTableColumn = (dataItemKey: string, data: Record<string, ProductDataAttribute>): ColumnType<any> => {
    return {
      dataIndex: ['data', dataItemKey, 'value'],
      key: dataItemKey,
      render: (value, data) => {
        if (!data?.data?.[dataItemKey]) {
          return value;
        }

        return <div dangerouslySetInnerHTML={{ __html: data.data[dataItemKey].display_value }} />;
      },
      sortDirections: ['ascend', 'descend'],
      sorter: data[dataItemKey]['sorting'] ? (a, b) => handleSorting(a, b, dataItemKey) : false,
      title: () => {
        return (
          <InformationTooltip title={GetTooltipInfoForColumn(dataItemKey)}>
            {data[dataItemKey].label}
          </InformationTooltip>
        );
      },
    };
  };

  const formatTableColumns = React.useCallback(
    (
      columnsKeys: string[],
      productData: Record<string, ProductDataAttribute>,
      customSettings: Record<string, any>,
    ): ColumnType<any>[] => {
      const columns: ColumnType<any>[] = [];

      if (Array.isArray(columnsKeys)) {
        columnsKeys.forEach(columnKey => {
          const columnSetting = formatTableColumn(columnKey, productData);

          if (customSettings[columnKey]) {
            Object.assign(columnSetting, customSettings[columnKey]);
          }

          columns.push(columnSetting);
        });
      }

      return columns;
    },
    [],
  );

  const getTableColumns = React.useCallback(() => {
    const isMobile = width !== 0 && width <= 660 && runningClientSide();

    let columns: ColumnType<any>[] = [];
    if (Array.isArray(products) && products.length) {
      const productData = products?.[0]?.data;

      if (productData) {
        const columnsKeys = isMobile ? Object.keys(customMobileColumnSettings) : Object.keys(productData);
        const customSettings = isMobile ? customMobileColumnSettings : customColumnSettings;

        columns = formatTableColumns(columnsKeys, productData, customSettings);
      }
    }

    return columns;
  }, [products, width, formatTableColumns]);

  return (
    <TableWrapper>
      <Filters
        beforeFilters={
          <div className="before_filters">
            <Checkbox
              key="low-risk"
              label="Remove Investments With Significant Principal Risk"
              onChange={handleSignificantRisk}
              value="true"
            />
            <InformationTooltip title="Remove listings that put a large percent of your money at risk" />
          </div>
        }
        filters={filters}
        layout="horizontal"
        onChange={handleFilterChange}
      />
      <Table
        columns={getTableColumns()}
        dataSource={filteredProducts()}
        pagination={false}
        rowKey="id"
        scroll={{ x: width > 660 ? 1000 : 0, y: 600 }}
        showSorterTooltip={false}
      />
    </TableWrapper>
  );
};

const GetTooltipInfoForColumn = (columnKey: string) => {
  switch (columnKey) {
    case 'yield_investments_risk_level':
      return 'The amount of risk your initial investment will be subjected to';
    case 'yield_investments_issuer':
      return 'The name of the organization offering the investment';
    case 'display_name':
      return 'The name of the investment being offered';
    case 'yield_investments_investment_horizon':
      return 'The amount of time your investment will be locked up for';
    case 'yield_investments_annual_yield_pct':
      return 'The annual return currently being offered on invested capital';
    case 'affiliate_link':
      return '';
    default:
      return '';
  }
};

export default YieldTable;

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  try {
    const session = getSessionSingleton();
    const contentManager = await session.getManager(ContentManager);
    const response = await contentManager.getProduct({
      vertical: 'yield-investments' as unknown as number,
    });
    return {
      props: {
        productFilters: (response.ok?.filters as Filter[]) ?? null,
        products: (response.ok?.products as Product[]) ?? null,
      },
    };
  } catch (error) {
    res.statusCode = 404;
    console.error('error:', error);

    return {
      props: {
        productFilters: null,
        products: null,
      },
    };
  }
};

const TableWrapper = styled.div`
  margin-bottom: 25px;
  background-color: #fff;

  .before_filters {
    align-items: center;
    display: flex;
  }
  .ant-table-tbody > tr > td {
    padding: 8px 16px !important;
  }

  .ant-table-column-sorters {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;

    .ant-table-column-sorter-inner {
      display: flex !important;
      flex-direction: column !important;

      .anticon {
        color: #e8e8e8;

        &.active {
          color: #000;
        }
      }
    }
  }
  thead > tr > th {
    font-weight: bold;
    position: relative;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    text-align: left;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }
`;

const InformationTooltip = ({ children, title }: { title: string; children?: React.ReactNode }) => {
  return (
    <Tooltip content={title} size="lg" trigger="hover" width={150}>
      <div>{children}</div>
      <Icon icon={faInfoCircle} style={{ marginLeft: 5 }} />
    </Tooltip>
  );
};
