import type { EChartsOption } from 'echarts';
import React from 'react';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

export interface RsiWidgetProps {
  value: number;
  style?: React.CSSProperties;
}

export const RsiWidget: React.FC<RsiWidgetProps> = ({ style, value }) => {
  let metric = 0;

  if (value > 1) {
    metric = value / 100;
  } else if (value < 0) {
    metric = 0;
  } else {
    metric = value;
  }
  const options = {
    series: [
      {
        axisLabel: {
          color: '#464646',
          distance: -65,
          fontSize: 0,
          formatter: function (value: number) {
            if (value === 0.875) {
              return 'Overbought';
            } else if (value === 0.125) {
              return 'Oversold';
            }
            return '';
          },
          rotate: 'tangential',
        },
        axisLine: {
          lineStyle: {
            color: [
              [0.3, '#FF6E76'],
              [0.7, '#CCCCCC'],
              [1, '#8EDA7B'],
            ],
            width: 6,
          },
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'inherit',
            width: 0,
          },
        },
        center: ['50%', '75%'],
        data: [
          {
            // name: label ? `RSI: ${label}` : 'RSI',
            value: metric,
          },
        ],
        detail: {
          color: 'inherit',
          fontSize: 30,
          formatter: function (value: number) {
            return Math.round(value * 100) + '';
          },
          offsetCenter: [0, '-15%'],
          valueAnimation: true,
        },
        endAngle: 0,
        max: 1,
        min: 0,
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          itemStyle: {
            color: 'inherit',
          },
          length: '25%',
          offsetCenter: [0, '-60%'],
          width: 20,
        },
        radius: '90%',
        splitLine: {
          length: 30,
          lineStyle: {
            color: 'inherit',
            width: 0,
          },
        },
        splitNumber: 8,
        startAngle: 180,
        title: {
          color: 'inherit',
          fontSize: 18,
          offsetCenter: [0, '-20%'],
        },
        type: 'gauge',
      },
    ],
  };

  return (
    <ReactECharts
      notMerge={true}
      option={options as EChartsOption}
      style={{ height: '100%', width: '100%', ...style }}
    />
  );
};
