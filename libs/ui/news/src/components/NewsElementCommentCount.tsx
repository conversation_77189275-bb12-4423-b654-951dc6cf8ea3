import React, { Suspense } from 'react';
import { Icon } from '@benzinga/core-ui';
import { faComment } from '@fortawesome/pro-light-svg-icons';
import { convertToRelativeUrl } from '@benzinga/utils';
import styled from 'styled-components';

const CommentsCountScript = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsCountScript })),
);

export const NewsElementCommentCount: React.FC<{
  id: number;
  url: string;
}> = ({ id, url }) => {
  return (
    <>
      <Suspense fallback={<div />}>
        <CommentsCountScript />
      </Suspense>

      <Container href={url ? `${convertToRelativeUrl(url)}?comments_open=true` : undefined}>
        <span className="flex items-center font-normal text-gray-500 hover:text-blue-400 hover:font-semibold">
          <Icon className="mr-1" icon={faComment} />
          <span className="coral-count text-xs" data-coral-id={id} data-coral-notext={true} data-coral-url={url} />
        </span>
      </Container>
    </>
  );
};

export default NewsElementCommentCount;

const Container = styled.a`
  .coral-count[data-coral-count='0'] {
    display: none;
  }
`;
