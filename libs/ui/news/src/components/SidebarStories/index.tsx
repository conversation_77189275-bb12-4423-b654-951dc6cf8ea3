import React from 'react';
import styled from '@benzinga/themetron';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { BzImage } from '@benzinga/image';
import { formatImageUrl } from '@benzinga/utils';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { News } from '@benzinga/basic-news-manager';

export interface SidebarStoriesProps {
  data: News[] | StoryObject[];
  title: string;
  showCount?: boolean;
  hideTickers?: boolean;
}

export const SidebarStories: React.FC<SidebarStoriesProps> = ({
  data,
  hideTickers = false,
  showCount = true,
  title,
}) => {
  if (!Array.isArray(data)) return null;
  return (
    <Container className="sidebar-stories">
      <div className="header-container">
        <h2>{title}</h2>
        <StyledLine />
      </div>
      <div className="stories-list">
        {data.map((item, index) => {
          if (!item) return <div />;
          const slicedStocks = Array.isArray(item.stocks) ? item.stocks.slice(0, 4) : [];
          const image = formatImageUrl(item);
          return (
            <div className="stories-list-item" key={index}>
              <div>
                {image && (
                  <a
                    aria-label="Click to go to this story"
                    className="story-image-container"
                    data-action="Sidebar Stories Item Click"
                    href={item.url}
                    rel="noreferrer"
                    target="_blank"
                  >
                    <BzImage
                      alt="Sidebar Stories Image"
                      height={75}
                      layout="fill"
                      objectFit="cover"
                      sizes="(max-width: 800px) 120px, (min-width: 800px) 120px"
                      src={image}
                      width={100}
                    />

                    {showCount && <div className="story-item-list-number-block">{index + 1}</div>}
                  </a>
                )}
                <Title className="story-title">
                  {!hideTickers && (
                    <>
                      {slicedStocks.map((stock, index: number) => {
                        const slug = stock.name.match(/^\$/)
                          ? `${stock.name.toLowerCase()?.replace('$', '')}/usd`
                          : stock.name.toLowerCase();
                        return (
                          <a
                            className="story-title-tickers"
                            data-action="Sidebar Stories Item Click"
                            href={`https://benzinga.com/quote/${slug}`}
                            key={index}
                            rel="noreferrer"
                            target="_blank"
                          >
                            {stock.name}
                            {slicedStocks.length - 1 !== index ? ', ' : ''}
                          </a>
                        );
                      })}
                      {slicedStocks.length > 0 && <span className="colon">: </span>}
                    </>
                  )}
                  <a
                    dangerouslySetInnerHTML={{ __html: sanitizeHTML(item?.title || '') }}
                    data-action="Sidebar Stories Item Click"
                    href={item.url}
                    key={index}
                    rel="noreferrer"
                    target="_blank"
                  />
                </Title>
              </div>
            </div>
          );
        })}
      </div>
    </Container>
  );
};

const Title = styled.h3`
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
`;

const Container = styled.div`
  &.sidebar-stories {
    margin-top: 2rem;

    .header-container {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;

      > h2 {
        z-index: 2;
        font-weight: 600;
        font-size: 1.25rem;
        line-height: 1;
        padding-right: 0.5rem;
        word-wrap: break-word;
        white-space: normal;
      }
    }

    .stories-list {
      > .stories-list-item {
        display: block;

        > div {
          display: flex;
          align-items: center;
          height: 6rem;
          padding-bottom: 0.75rem;
          margin-bottom: 0.75rem;
          border-bottom: 1px solid rgb(233, 235, 229);

          .story-image-container {
            display: block;
            position: relative;
            height: 75px;
            width: 100px;
            min-width: 100px;

            .story-item-list-number-block {
              color: ${({ theme }) => theme.colorPalette.white};
              font-weight: 700;
              text-align: center;
              background-color: rgba(0, 0, 0, 0.5);
              width: 28px;
              height: 28px;
              line-height: 28px;
              position: absolute;
              left: 0px;
              bottom: 0px;
            }
          }

          > .story-title {
            font-weight: 700;
            font-size: 0.9rem;
            line-height: 1.25rem;
            margin-left: 0.5rem;

            .colon {
              color: ${({ theme }) => theme.colorPalette.blue500};
            }

            .story-title-tickers {
              color: ${({ theme }) => theme.colorPalette.blue500};
              cursor: pointer;

              &:hover {
                color: ${({ theme }) => theme.colorPalette.blue600};
                text-decoration: none;
              }
            }

            > a {
              color: ${({ theme }) => theme.colors.foreground};
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
`;

const StyledLine = styled.div`
  flex-grow: 1;
  position: relative;
  height: 1px;

  &:before {
    content: '';
    width: 100%;
    height: 1px;
    display: block;
    background-color: #d6d6d6;
    position: absolute;
    top: 50%;
  }
`;
