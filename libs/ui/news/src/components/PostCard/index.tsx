import React, { useMemo } from 'react';
import { BzImage, type PreloadOptions, defaultImageLoader, LoadingValue, FetchPriority } from '@benzinga/image';
//import NextImage from 'next/legacy/image';
import styled from '@benzinga/themetron';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { StoryCategory } from '@benzinga/advanced-news-manager';
import { PostDateType, PostElapsed } from './PostElapsed';
import { NodeCategory } from '@benzinga/basic-news-manager';
import { decode } from 'html-entities';
import { appEnvironment, appName, formatImageUrl } from '@benzinga/utils';
import NewsElementCommentCount from '../NewsElementCommentCount';
import { NoFirstRender } from '@benzinga/hooks';

const PLACEHOLDER = '/next-assets/images/benzinga-post-card-placeholder.svg';
const BZ_PRO_PLACEHOLDER = '/next-assets/images/benzinga-pro-post-card-placeholder-light.svg';

export type PostCardLayout = 'default' | 'hero' | 'feed' | 'card' | 'india-primary' | 'india-secondary';
export type PostCardSize = 'default' | 'compact' | 'nano';

export interface PostCardProps {
  author?: string | null | undefined;
  created?: string;
  dataAction?: string;
  dateType?: PostDateType;
  description?: string | null;
  hideEmptyThumb?: boolean;
  hideImages?: boolean;
  image?: string | { url: string }[];
  isOnlyProPost?: boolean;
  layout?: PostCardLayout;
  size?: PostCardSize;
  sizes?: string;
  sponsored?: boolean;
  title?: string | null;
  hidePublishDate?: boolean;
  loading?: LoadingValue;
  stocks?: StoryCategory[] | NodeCategory[] | null;
  id?: number;
  url?: string;
  imageWidth?: number;
  imagePreload?: boolean;
  preloadOptions?: PreloadOptions;
  fetchPriority?: FetchPriority;
  showCommentsIcon?: boolean;
  styles?: {
    titleLineHeight: 'normal' | 'snug' | 'tight' | 'loose';
  };
}

export const PostCard: React.FC<PostCardProps> = React.memo(
  ({
    created,
    dataAction,
    dateType = 'relative',
    description,
    fetchPriority,
    hideEmptyThumb = false,
    hideImages = false,
    hidePublishDate = false,
    id,
    image,
    imagePreload,
    imageWidth = undefined,
    isOnlyProPost = false,
    layout = 'default',
    loading,
    preloadOptions = {},
    showCommentsIcon = false,
    size = 'default',
    sizes = '(max-width: 800px) 120px, (min-width: 800px) 240px',
    sponsored,
    styles,
    title,
    url,
  }) => {
    const postCardTitleClassname = `post-card-title ${styles?.titleLineHeight ? `leading-${styles?.titleLineHeight}` : `leading-${layout === 'hero' ? 'tight' : 'normal'}`} text-${
      size === 'compact' ? 'base' : layout === 'hero' && Number(title?.length) < 60 ? '4xl' : '2xl'
    }`;

    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const isImageExpired = new Date(created ?? Date.now()) < oneYearAgo;

    const imageSrc = !isImageExpired && image && formatImageUrl({ image }, true, false);

    const isIndiaApp = appEnvironment().isApp(appName.india);

    const IndiaFeaturedPostCard: React.FC = () => {
      return (
        <PostCardContainer
          className={`post-card post-card-${layout} post-card-size-${size}`}
          layout={layout}
          size={size}
          title={title ? decode(title) : undefined}
        >
          {image && !hideImages && (
            <a data-action={dataAction} href={url}>
              <div className="image-wrapper-india">
                <Thumb
                  alt=""
                  className="post-thumb"
                  fetchPriority={fetchPriority}
                  layout="fill"
                  loader={imageProps =>
                    defaultImageLoader(
                      imageWidth
                        ? { ...imageProps, width: imageWidth }
                        : layout === 'feed'
                          ? { ...imageProps, width: imageWidth || 260 }
                          : imageProps,
                    )
                  }
                  loading={loading}
                  objectFit="cover"
                  preload={imagePreload}
                  preloadOptions={preloadOptions}
                  sizes={sizes}
                  src={imageSrc || (isOnlyProPost ? BZ_PRO_PLACEHOLDER : PLACEHOLDER)}
                />
                <GradientBackground layout={layout}>
                  <div className={`post-elapsed-${layout}`}>
                    {created && <PostElapsed created={created} dateType={dateType} layout="secondary" />}
                  </div>

                  <PostTitleIndia className={`post-title-${layout}`}>
                    {title && <span dangerouslySetInnerHTML={sanitizedTitle} />}
                  </PostTitleIndia>

                  {layout === 'india-primary' && (
                    <PostDescriptionIndia className="post-description">
                      {description && <span dangerouslySetInnerHTML={sanitizedDescription} />}
                    </PostDescriptionIndia>
                  )}
                </GradientBackground>
              </div>
            </a>
          )}
        </PostCardContainer>
      );
    };

    const sanitizedTitle = useMemo(() => {
      return { __html: title ? sanitizeHTML(decode(title)) : '' };
    }, [title]);

    const sanitizedDescription = useMemo(() => {
      return { __html: description ? sanitizeHTML(description) : '' };
    }, [description]);

    return (
      <PostCardWrapper $isIndiaApp={isIndiaApp} $layout={layout} className="post-card-wrapper">
        {layout === 'india-primary' || layout === 'india-secondary' ? (
          <IndiaFeaturedPostCard />
        ) : (
          <PostCardContainer
            className={`post-card post-card-${layout} post-card-size-${size}`}
            layout={layout}
            size={size}
            title={title ? decode(title) : undefined}
          >
            {(image || !hideEmptyThumb) && (
              <a className="post-card-article-link post-card-image image-wrapper" data-action={dataAction} href={url}>
                <Thumb
                  alt=""
                  className="post-thumb"
                  layout="fill"
                  loader={imageProps =>
                    defaultImageLoader(
                      imageWidth
                        ? { ...imageProps, width: imageWidth }
                        : layout === 'feed'
                          ? { ...imageProps, width: imageWidth || 260 }
                          : imageProps,
                    )
                  }
                  loading={loading}
                  objectFit="cover"
                  preload={imagePreload}
                  preloadOptions={preloadOptions}
                  sizes={sizes}
                  src={imageSrc || (isOnlyProPost ? BZ_PRO_PLACEHOLDER : PLACEHOLDER)}
                />
              </a>
            )}

            <div className="post-card-text lg:w-auto flex flex-col space-y-2">
              <div className="post-card-text-wrapper">
                <a className="post-card-article-link" data-action={dataAction} href={url}>
                  {size === 'nano' ? (
                    <PostTitle className="post-title">
                      <ThreeLines className={`line-wrapper three-line ${postCardTitleClassname}`}>
                        {title && <span dangerouslySetInnerHTML={sanitizedTitle} />}
                      </ThreeLines>
                    </PostTitle>
                  ) : (
                    <PostTitle className="post-title">
                      <DynamicLines className={`line-wrapper ${postCardTitleClassname}`}>
                        {title && <span dangerouslySetInnerHTML={sanitizedTitle} />}
                      </DynamicLines>
                    </PostTitle>
                  )}
                  {description && (layout === 'feed' || layout === 'card') && (
                    <div
                      className={`post-card-description lg:${
                        size === 'compact' || size === 'nano' ? 'text-xs' : 'text-lg'
                      }`}
                    >
                      {!hidePublishDate && !sponsored && created && isIndiaApp && (
                        <>
                          <PostElapsed
                            className="font-semibold text-gray-500 post-created-at mr-1"
                            created={created}
                            dateType={dateType}
                          />
                          {description?.trim() && <span className="font-semibold text-gray-500 mr-1">|</span>}
                          {showCommentsIcon && !sponsored && id && url && (
                            <NoFirstRender>
                              <div className="flex items-center ml-2 text-sm text-gray-500">
                                {!hidePublishDate && <span className="mr-2 font-light">|</span>}
                                <NewsElementCommentCount id={id} url={url} />
                              </div>
                            </NoFirstRender>
                          )}
                        </>
                      )}
                      <span className="post-teaser" dangerouslySetInnerHTML={sanitizedDescription} />
                    </div>
                  )}
                </a>
                <div className="post-card-article-link flex items-center">
                  {!sponsored && layout !== 'hero' && created && !hidePublishDate && !isIndiaApp ? (
                    <PostElapsed
                      className="font-normal text-gray-500 post-created-at"
                      created={created}
                      dateType={dateType}
                    />
                  ) : null}
                  {showCommentsIcon && id && url && (
                    <NoFirstRender>
                      <div className="flex items-center ml-2 text-sm text-gray-500">
                        {!hidePublishDate && <span className="mr-2 font-light">|</span>}
                        <NewsElementCommentCount id={id} url={url} />
                      </div>
                    </NoFirstRender>
                  )}
                </div>
                {sponsored ? <span className="news-item-sponsored-tag">Sponsored</span> : null}
              </div>
            </div>
          </PostCardContainer>
        )}
      </PostCardWrapper>
    );
  },
);

const PostCardWrapper = styled.div<{ $isIndiaApp: boolean; $layout: PostCardLayout }>`
  &.post-card-wrapper {
    display: ${({ $isIndiaApp }) => ($isIndiaApp ? 'block' : 'flex')};
    .post-card-article-link {
      color: ${({ theme }) => theme.colorPalette.black};
      &:hover {
        color: ${({ theme }) => theme.colorPalette.blue400};
      }
    }
    .post-card-hero,
    .post-card-hero-compact {
      background: ${({ theme }) => theme.colorPalette.neutral900};
      @media screen and (max-width: 1024px) {
        margin-bottom: 12px;
      }
      .post-card-text {
        background: ${({ theme }) => theme.colorPalette.neutral900};
        color: white;
        margin-top: -3em;
        z-index: 0;
        &:before {
          content: ' ';
          background: linear-gradient(transparent, ${({ theme }) => theme.colorPalette.neutral900});
          height: 3em;
          margin-top: -3em;
          position: relative;
        }
        .post-card-text-wrapper {
          padding: 0.5em 0.75em;
          min-height: 56px;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .post-card-description {
      font-size: 12px;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      color: ${({ theme }) => theme.colorPalette.gray700};
      font-weight: ${({ theme }) => theme.fontWeight.normal};
      line-height: 1.25rem;
    }

    .news-item-sponsored-tag {
      display: block;
      font-size: 13px;
      color: ${({ theme }) => theme.colorPalette.blue500};
    }

    .image-wrapper {
      position: relative;
      overflow: hidden;
      border-radius: ${({ theme }) => theme.borderRadius.sm};
      display: inline-flex;
      flex: 1;
      width: 100%;
      height: 100%;
      img {
        object-fit: cover;
      }
    }

    .image-wrapper-india {
      position: relative;
      overflow: hidden;
      border-radius: 8px;
      display: inline-flex;
      flex: 1;
      width: 100%;
      height: 100%;
      img {
        object-fit: cover;
      }
    }

    .post-elapsed-india-primary {
      margin-top: 24px;
      margin-left: 24px;
      height: 22px;
      color: #b8cbe5;
    }

    .post-elapsed-india-secondary {
      margin-top: 16px;
      margin-left: 16px;
      height: 18px;
      color: #b8cbe5;
    }

    .post-card-default {
      .image-wrapper {
        height: 360px;

        @media (max-width: 768px) {
          height: 108px;
        }
      }
    }

    .post-card-card {
      border-radius: ${({ theme }) => theme.borderRadius.md};
      background: ${({ theme }) => theme.colorPalette.white};
      border: 1px solid ${({ theme }) => theme.colors.border};

      .image-wrapper {
        height: 130px;
        margin-bottom: 0;
        display: block;
      }
      .post-card-text-wrapper {
        padding: 1rem;
      }
      .post-title {
        font-size: ${({ theme }) => theme.fontSize.lg};
        margin-bottom: 0.25rem;
        display: block;
      }
      .post-teaser {
        font-size: ${({ theme }) => theme.fontSize.lg};
        color: ${({ theme }) => theme.colorPalette.gray500};
        font-weight: ${({ theme }) => theme.fontWeight.light};
      }
    }

    .post-card-feed {
      .post-card-description {
        font-size: ${({ theme }) => theme.fontSize.md};
      }
      .image-wrapper {
        max-height: 126px;
        min-width: 160px;
        max-width: 160px;
        min-height: 126px;

        @media (max-width: 768px) {
          max-height: 108px;
          min-height: 108px;
        }
      }
    }

    .post-card-size-compact {
      .image-wrapper {
        max-height: 180px;
        @media (max-width: 768px) {
          max-height: 108px;
          min-height: 108px;
        }
      }
    }

    .post-card-size-nano {
      .image-wrapper {
        max-height: 108px;

        @media (max-width: 768px) {
          max-height: 120px;
        }
      }
    }

    .post-card-feed {
      .post-card-title {
        font-size: 20px;
        -webkit-line-clamp: 3;
      }
      .post-card-image {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }
    @media screen and (max-width: 800px) {
      .post-card-hero {
        .post-card-title {
          font-size: ${({ theme }) => theme.fontSize.xl};
        }
      }
      .post-card-feed {
        display: inline-flex;
        flex-direction: row;
        .post-card-title {
          font-size: ${({ theme }) => theme.fontSize.xl};
        }
        .post-card-image {
          height: 100px;
          min-height: 100px;
          min-width: 100px;
          max-height: 100px;
          max-width: 100px;
          width: 100px;
        }
        .post-card-text {
          margin-left: 0;
        }
        .post-card-description {
          font-size: 12px;
          font-weight: normal;
          line-height: 18px;
        }
      }
    }
    @media screen and (max-width: 500px) {
      .post-card-description {
        display: none;
      }
    }
  }
`;

interface PostCardContainerProps {
  layout?: PostCardLayout;
  size?: PostCardSize;
  title?: string;
}

const titleSize = (title?: string, size?: PostCardSize, layout?: PostCardLayout): string => {
  return size === 'compact' ? 'base' : layout === 'hero' && Number(title?.length) < 60 ? '4xl' : '2xl';
};

const PostCardContainer = styled.div<PostCardContainerProps>`
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: ${({ layout }) => (layout && ['feed', 'hero'].includes(layout) ? 'flex' : 'block')};
  font-size: ${({ layout, theme }) => (layout === 'default' ? theme.fontSize.base : theme.fontSize.xl)};
  font-weight: ${({ theme }) => theme.fontWeight.semibold};
  overflow: hidden;
  flex-direction: ${({ layout }) => (layout === 'feed' ? 'row' : 'column')};
  .post-card-title {
    font-size: ${({ layout, size, theme, title }) => theme.fontSize[titleSize(title, size, layout)]};
  }
`;

const PostTitle = styled.span`
  &.post-title {
    line-height: 1.4em;
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    display: -webkit-box;

    @media screen and (max-width: 600px) {
      font-size: 0.95rem;
      line-height: 1.4rem;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inherit;
    }
  }
`;

const PostTitleIndia = styled.span`
  &.post-title-india-primary {
    font-weight: 700;
    line-height: 28px;
    font-size: 20px;
    height: 84px;
    color: #fff;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    width: 316px;
    margin-top: 44px;
    margin-left: 24px;

    @media screen and (max-width: 600px) {
      font-size: 0.95rem;
      line-height: 1.4rem;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inherit;
    }
  }
  &.post-title-india-secondary {
    font-weight: 700;
    line-height: 24px;
    height: 72px;
    font-size: 16px;
    color: #fff;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    width: 252px;
    margin-top: 78px;
    margin-left: 16px;

    @media screen and (max-width: 600px) {
      font-size: 0.95rem;
      line-height: 1.4rem;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inherit;
    }
  }
`;

const GradientBackground = styled.div<{ layout: PostCardLayout }>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid #000;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  background: ${({ layout }) =>
    layout === 'india-primary'
      ? 'radial-gradient(143.64% 167.08% at 140% -38%, rgba(255, 255, 255, 0) 0%, #192940 101%)'
      : layout === 'india-secondary'
        ? 'radial-gradient(83.22% 75.41% at 64.51% 26%, rgba(255, 255, 255, 0) 0%, #192940 100%)'
        : ''};
`;

const PostDescriptionIndia = styled.span`
  &.post-description {
    margin-top: 16px;
    margin-left: 24px;
    height: 66px;
    width: 425px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-family: Manrope, Manrope-fallback, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #b8cbe5;
  }
`;

const Thumb = styled(BzImage)`
  &.post-thumb {
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    width: 100%;
    position: relative;
    margin-right: 16px;
  }
`;

const LineWrapper = styled.div`
  &.line-wrapper {
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

// const DoubleLine = styled(LineWrapper)`
//   &.double-line {
//     font-size: 0.7rem;
//     -webkit-line-clamp: 2;
//   }
// `;

const ThreeLines = styled(LineWrapper)`
  &.three-line {
    -webkit-line-clamp: 4;
  }
`;

const DynamicLines = styled(LineWrapper)`
  -webkit-line-clamp: 6;
  font-size: 1.5rem;

  @media screen and (max-width: 600px) {
    font-size: 0.7rem;
    -webkit-line-clamp: 3;
  }
`;
