import { faChevronDown } from '@fortawesome/pro-regular-svg-icons/faChevronDown';
import { Icon } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import { menus } from './Header/_mockMenu';
import { appEnvironment } from '@benzinga/utils';
import { DEUFlag, ESPFlag, FRAFlag, INDFlag, ITAFlag, JPNFlag, KORFlag, USAFlag } from '@benzinga/icons';

// This is to prevent large js bundles
const CountryCodes = {
  DEU: DEUFlag,
  ESP: ESPFlag,
  FRA: FRAFlag,
  IND: INDFlag,
  ITA: ITAFlag,
  JPN: JPNFlag,
  KOR: KORFlag,
  USA: USAFlag,
};

export const GlobalMenu = () => {
  if (!appEnvironment().isApp()) {
    return null;
  }

  const USAIcon: React.FunctionComponent<
    React.SVGProps<SVGSVGElement> & {
      title?: string | undefined;
    }
  > = USAFlag;

  return (
    <MenuWrapper className="global-menu menu-wrapper">
      <div aria-label="Global Menu" className="menu-button" role="button">
        <span className="global-text w-[25px] h-[16px]">
          <USAIcon title="USA" />
        </span>
        <Icon icon={faChevronDown} />
      </div>
      <div className="global-menu-dropdown">
        {menus?.globalMenu[0]?.subnav?.links?.map((item, i: number) => {
          const CountryIcon: React.FunctionComponent<
            React.SVGProps<SVGSVGElement> & {
              title?: string | undefined;
            }
          > = CountryCodes[`${item.img_key}`];
          return (
            <div className="global-menu-item" key={i}>
              <a href={item.href} rel="noreferrer" tabIndex={0} target="_blank">
                <span className="w-[25px] h-[16px]">
                  <CountryIcon title={item.label} />
                </span>
                <span>{item.label}</span>
              </a>
            </div>
          );
        })}
      </div>
    </MenuWrapper>
  );
};

const MenuWrapper = styled.div`
  &.menu-wrapper {
    background-color: #033251;
    position: relative;
    min-width: 65px;
    &:hover {
      .global-menu-dropdown {
        display: block;
      }
    }
    @media screen and (max-width: 800px) {
      background-color: transparent;
    }
    .menu-button {
      align-items: center;
      color: white;
      cursor: pointer;
      display: inline-flex;
      font-size: 12px;
      height: 32px;
      padding: 0px 12px;
      .anticon-user {
        color: #1a79ff;
        margin-right: 12px;
      }
      @media screen and (max-width: 800px) {
        padding: 0px 6px;
        .anticon-user {
          color: ${({ theme }) => theme.colorPalette.blue300};
          font-size: 18px;
        }
        .anticon-down {
          display: none;
        }
      }
    }
    .global-menu-dropdown {
      background-color: #033251;
      display: none;
      position: absolute;
      width: 110px;
      width: max-content;
      z-index: 100;
      @media screen and (max-width: 800px) {
        right: 0px;
      }
    }
    .global-menu-item {
      color: white;
      cursor: pointer;
      font-size: 12px;
      padding: 8px 12px;
      a {
        color: white;
        display: flex;
        align-items: center;
      }
      display: block;
      margin: 0;
      span {
        display: inline-block;
        vertical-align: middle;
        line-height: 1;
        margin-right: 10px;
      }
      img {
        width: 30px;
        display: inline-block;
        vertical-align: middle;
      }
    }
    .global-text {
      margin-right: 14px;
      @media screen and (max-width: 800px) {
        margin-right: 0;
      }
    }
    @media screen and (max-width: 800px) {
      width: 3rem;
      text-align: center;
    }
  }
`;
