'use client';

/**
 * Toast notification system for @benzinga/core-ui
 *
 * This component provides a replacement for antd's message component using react-hot-toast.
 *
 * Usage:
 * 1. Wrap your app with ToastProvider
 * 2. Use the toast object for global access
 *
 * Example:
 * ```tsx
 * import { ToastProvider, toast } from '@benzinga/core-ui';
 *
 * // In your app root:
 * <ToastProvider>
 *   <App />
 * </ToastProvider>
 *
 * // In components:
 * toast.success('Success message');
 * toast.error('Error message');
 * toast.loading('Loading...'); // Auto-dismiss after duration
 * ```
 */

import React from 'react';
import hotToast, { Toaster } from 'react-hot-toast';
import styled from '@benzinga/themetron';

export type ToastType = 'success' | 'error' | 'warning' | 'loading';

interface ToastConfig {
  content: React.ReactNode;
  duration?: number;
  icon?: React.ReactNode;
  style?: React.CSSProperties;
}

// Styled Toaster component
const StyledToaster = styled(Toaster)`
  .react-hot-toast-toast {
    background: ${({ theme }) => theme.colors.background || '#fff'};
    color: ${({ theme }) => theme.colors.foreground || '#333'};
    border: 1px solid ${({ theme }) => theme.colors.border || '#e5e5e5'};
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

// ToastProvider component that wraps the app
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <>
      {children}
      <StyledToaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: 'var(--toast-bg, #fff)',
            border: '1px solid var(--toast-border, #e5e5e5)',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            color: 'var(--toast-color, #333)',
          },
          error: {
            iconTheme: {
              primary: 'var(--toast-error-color, #ff4d4f)',
              secondary: 'var(--toast-error-bg, #fff)',
            },
          },
          success: {
            iconTheme: {
              primary: 'var(--toast-success-color, #52c41a)',
              secondary: 'var(--toast-success-bg, #fff)',
            },
          },
        }}
      />
    </>
  );
};

// Create a toast API compatible with antd's message API
export const toast = {
  destroy: (id?: string) => {
    if (id) {
      hotToast.dismiss(id);
    } else {
      hotToast.dismiss();
    }
  },

  error: (content: React.ReactNode, duration?: number) => {
    return hotToast.error(content as string, {
      duration: (duration ?? 3) * 1000,
    });
  },

  loading: (content: React.ReactNode, duration?: number) => {
    return hotToast.loading(content as string, {
      duration: duration === 0 ? Infinity : (duration ?? 3) * 1000,
    });
  },

  success: (content: React.ReactNode | ToastConfig, duration?: number) => {
    if (typeof content === 'object' && content !== null && 'content' in content) {
      const config = content as ToastConfig;
      return hotToast.success(config.content as string, {
        duration: (config.duration ?? 3) * 1000,
        style: config.style,
      });
    }
    return hotToast.success(content as string, {
      duration: (duration ?? 3) * 1000,
    });
  },

  warning: (content: React.ReactNode, duration?: number) => {
    return hotToast(content as string, {
      duration: (duration ?? 3) * 1000,
      icon: '⚠️',
    });
  },
};
