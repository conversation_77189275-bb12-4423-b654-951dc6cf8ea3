'use client';

import React from 'react';
import styled from '@benzinga/themetron';
import { faFrown } from '@fortawesome/pro-regular-svg-icons/faFrown';
import { Icon } from '@benzinga/core-ui';
import classnames from 'classnames';

export interface NoResultsProps {
  className?: string;
  title?: string;
  subtitle?: string;
}

export const NoResults: React.FC<React.PropsWithChildren<NoResultsProps>> = ({
  children,
  className = '',
  subtitle,
  title,
}) => {
  return (
    <Container className={classnames('no-results', { [`${className}`]: !!className })}>
      <Icon icon={faFrown} className="frown-icon" />
      {title && <p className="title">{title}</p>}
      {subtitle && <span className="subtitle">{subtitle}</span>}
      {children && <div className="children">{children}</div>}
    </Container>
  );
};

const Container = styled.div`
  &.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem 0;

    .frown-icon {
      font-size: ${({ theme }) => theme.fontSize['5xl']};
      margin-bottom: 10px;
      color: #c4c4c4;
    }

    .title {
      font-size: ${({ theme }) => theme.fontSize['xl']};
      margin-bottom: 5px;
    }

    .subtitle {
      font-size: ${({ theme }) => theme.fontSize.base};
    }
  }
`;
