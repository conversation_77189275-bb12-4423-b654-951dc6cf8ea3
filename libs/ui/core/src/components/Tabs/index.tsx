'use client';

import React, { startTransition } from 'react';
// import Link from 'next/link';
import Hooks from '@benzinga/hooks';
import styled from '@benzinga/themetron';
import { Icon } from '../Icon';
import { IconDefinition } from '@fortawesome/fontawesome-common-types';
import { faEllipsisH } from '@fortawesome/pro-light-svg-icons/faEllipsisH';
import SelectMenu from '../SelectMenu';
import classnames from 'classnames';
import { checkOverflow } from '@benzinga/frontend-utils';
import LazyLoad from 'react-lazyload';

type TabVariant = 'default' | 'bordered' | 'lifted' | 'pill' | 'full';

export interface TabsInterface {
  component?: React.ReactElement;
  key: string;
  link?: string;
  externalLink?: string;
  icon?: IconDefinition;
  name: string;
}

export interface TabsProps {
  tabs: TabsInterface[];
  onChange?: (tabKey: string) => void;
  activeTab: string;
  enableLazyLoad?: boolean;
  lazyLoadOptions?: {
    height: 400;
    offset: 200;
  };
  variant?: TabVariant;
  isShowMoreButtonEnabled?: boolean;
  tabListWrapperClassname?: string;
  tabBarGutter?: number;
  showScrollShadow?: boolean;
  leftSideElement?: React.ReactNode;
  rightSideElement?: React.ReactNode;
}

export const Tabs: React.FC<TabsProps> = ({
  activeTab,
  enableLazyLoad = false,
  isShowMoreButtonEnabled = true,
  lazyLoadOptions = {
    height: 400,
    offset: 200,
  },
  leftSideElement = null,
  onChange,
  rightSideElement = null,
  showScrollShadow = true,
  tabBarGutter,
  tabListWrapperClassname,
  tabs,
  variant = 'default',
}) => {
  const tabListRef = React.useRef<HTMLDivElement>(null);
  const selectMenuRef = React.useRef<HTMLDivElement>(null);
  const moreItemsButtonRef = React.useRef<HTMLButtonElement>(null);
  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);
  const [visibleTabIndices, setVisibleTabIndices] = React.useState<number[]>([]);
  const [isMounted, setIsMounted] = React.useState(false);
  const [isCalculating, setIsCalculating] = React.useState(false);
  const shouldCalculate = variant !== 'full';
  const { width } = Hooks.useWindowSize();
  const mobileWidth = width < 760;

  const shouldShowScrollShadow = !isShowMoreButtonEnabled && showScrollShadow;

  React.useEffect(() => {
    const isOverflowing = tabListRef.current && checkOverflow(tabListRef.current, true);
    if (shouldShowScrollShadow && tabListRef.current && isOverflowing) {
      tabListRef.current.className = 'tab-list overflow-shadow-right';
    }
  }, [shouldShowScrollShadow]);

  React.useEffect(() => {
    startTransition(() => {
      setIsMounted(true);
      shouldCalculate && setIsCalculating(true);
    });
  }, [shouldCalculate]);

  React.useEffect(() => {
    if (!isShowMoreButtonEnabled) return;
    const tabList = tabListRef?.current;
    if (tabList && isCalculating) {
      tabList.style.overflowX = 'auto';
      const tabElements = Array.from(tabList.children) as HTMLElement[];
      let stopWidth = 36;
      const visible: number[] = [];
      tabElements.forEach((tab, index) => {
        if (moreItemsButtonRef && moreItemsButtonRef.current && visible.length === tabElements.length - 1) {
          stopWidth -= moreItemsButtonRef.current.offsetWidth;
        }

        stopWidth += tab.offsetWidth;
        if (tabList && tabList.offsetWidth >= stopWidth) {
          visible.push(index);
        }
      });

      setVisibleTabIndices(visible);
      tabList.style.overflowX = 'unset';
      setIsCalculating(false);
    }
  }, [tabs, isShowMoreButtonEnabled, isCalculating, shouldCalculate]);

  Hooks.useEventListener('resize', () => {
    if (!shouldCalculate) return;
    if (mobileWidth) return;
    if (!isCalculating) {
      startTransition(() => {
        setIsCalculating(true);
      });
    }
    startTransition(() => {
      setIsDropdownOpen(false);
    });
  });

  Hooks.useEventListener(
    'scroll',
    event => {
      if (!shouldCalculate) return;
      if (!tabListRef.current) return;
      if (!shouldShowScrollShadow) return;
      tabListRef.current.className = 'tab-list overflow-shadow-right';

      const element = event.target as HTMLElement;
      if (element.scrollLeft < 10) {
        tabListRef.current.className = 'tab-list overflow-shadow-right';
      } else if (element.scrollLeft + element.clientWidth >= element.scrollWidth) {
        tabListRef.current.className = 'tab-list overflow-shadow-left';
      } else {
        tabListRef.current.className = 'tab-list overflow-shadow-both';
      }
    },
    tabListRef as React.RefObject<HTMLElement>,
    { removeEventListener: !shouldShowScrollShadow },
  );

  Hooks.useClickOutside(selectMenuRef, (event?: MouseEvent) => {
    if (!event) {
      setIsDropdownOpen(false);
      return;
    }
    if (
      shouldCalculate &&
      moreItemsButtonRef.current &&
      event.target &&
      !moreItemsButtonRef.current?.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  });

  const handleChangeTab = React.useCallback(
    (newTabKey: string) => {
      if (typeof onChange === 'function') onChange(newTabKey);
    },
    [onChange],
  );

  const handleToggleMoreItems = () => setIsDropdownOpen(!isDropdownOpen);

  let visibleTabs: TabsInterface[] = [];
  const overflowTabs: TabsInterface[] = [];
  if (!isMounted || isCalculating || !shouldCalculate) {
    visibleTabs = tabs;
  } else {
    tabs.forEach((tab, index) => {
      if (visibleTabIndices.includes(index)) {
        visibleTabs.push(tab);
      } else {
        overflowTabs.push(tab);
      }
    });
  }

  const isDropdownTabShown = !isCalculating && isDropdownOpen && overflowTabs.length > 0;

  return (
    <TabsContainer
      $tabBarGutter={tabBarGutter}
      className={classnames('tabs-container', {
        [`${variant}-variant`]: variant,
        'show-more-enabled': isShowMoreButtonEnabled,
      })}
    >
      <div
        className={classnames('tab-list-wrapper', {
          [`${tabListWrapperClassname}`]: tabListWrapperClassname,
        })}
      >
        {leftSideElement}
        <div className="tab-list" ref={tabListRef}>
          {Array.isArray(visibleTabs) &&
            visibleTabs.map(tab => {
              if (tab?.externalLink) {
                return (
                  <a
                    className="tab"
                    data-tab-link-external={tab.key}
                    href={tab.externalLink}
                    key={tab.key}
                    rel="noreferrer"
                    target="_blank"
                  >
                    <TabButton activeTab={activeTab} tab={tab} />
                  </a>
                );
              } else if (tab?.link) {
                return (
                  <a className="tab" href={tab.link} key={tab.key}>
                    <TabButton activeTab={activeTab} tab={tab} />
                  </a>
                );
              }
              return (
                <TabButton activeTab={activeTab} key={tab.key} onClick={() => handleChangeTab(tab.key)} tab={tab} />
              );
            })}
          {shouldCalculate && overflowTabs?.length > 0 && (
            <button
              aria-label="Click to see more tab items"
              className="more-items-button"
              onClick={handleToggleMoreItems}
              ref={moreItemsButtonRef}
            >
              <Icon className="ellipsis-icon" icon={faEllipsisH} />
            </button>
          )}
          {isShowMoreButtonEnabled && shouldCalculate && (
            <div className="secondary-tabs-dropdown" ref={selectMenuRef}>
              <SelectMenu
                onChange={handleChangeTab}
                open={isDropdownTabShown}
                optionElement={option => {
                  if (option.externalLink) {
                    return (
                      <a
                        className="tab"
                        data-tab-link-external={option.key}
                        href={option.externalLink}
                        key={option.key}
                        rel="noreferrer"
                        target="_blank"
                      >
                        {option.name}
                      </a>
                    );
                  }
                  if (option.link) {
                    return (
                      <a href={option.a} key={option.key}>
                        {option.name}
                      </a>
                    );
                  }
                  return option.name;
                }}
                options={overflowTabs}
                position="right"
                selected={activeTab}
              />
            </div>
          )}
        </div>
        {rightSideElement}
      </div>
      <div className="tabs-content">
        {Array.isArray(tabs) &&
          tabs.map(({ component, key }) => {
            if (activeTab === key || !enableLazyLoad) {
              return (
                <div
                  className={classnames('tab-content', {
                    selected: activeTab === key,
                  })}
                  key={`tab-content-${key}`}
                >
                  {component}
                </div>
              );
            }

            return (
              <div
                className={classnames('tab-content', {
                  selected: activeTab === key,
                })}
                key={`tab-content-${key}`}
              >
                <LazyLoad height={lazyLoadOptions?.height || 400} offset={lazyLoadOptions?.offset || 200} once>
                  <>{component}</>
                </LazyLoad>
              </div>
            );
          })}
      </div>
    </TabsContainer>
  );
};

const TabButton: React.FC<{ activeTab: string; tab: TabsInterface; onClick?: () => void }> = ({
  activeTab,
  onClick,
  tab,
}) => {
  return (
    <button
      className={classnames('tab', { active: activeTab === tab.key, 'promo-offer': tab.key === 'offer' })}
      key={`tab-button-${tab.key}`}
      name={tab.name}
      onClick={onClick}
    >
      {tab.icon && <Icon className="mr-1.5" icon={tab.icon} />}
      {tab.name}
    </button>
  );
};

const TabsContainer = styled.div<{ $tabBarGutter?: number }>`
  &.tabs-container {
    &.full-variant {
      &.tab-list-wrapper {
        overflow-x: auto;
        overflow-y: hidden;
        min-width: 0;
        & {
          -ms-overflow-style: none; /* for Internet Explorer, Edge */
          scrollbar-width: none; /* for Firefox */
        }
        &::-webkit-scrollbar {
          display: none; /* for Chrome, Safari, and Opera */
        }
      }

      &.tab-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 8px;
        min-width: max-content;
        padding-top: 14px;
        padding-bottom: 14px;

        &button.tab {
          width: 100%;
          border-radius: ${({ theme }) => theme.borderRadius.default};
          background-color: ${({ theme }) => theme.colorPalette.white};
          border: 1px solid ${({ theme }) => theme.colorPalette.gray100};
          color: ${({ theme }) => theme.colorPalette.gray500};
          font-weight: ${({ theme }) => theme.fontWeight.bold};
          font-size: 14px;
          height: 36px;
          padding: 0 16px;
          transition:
            0.25s color ease-in-out,
            0.25s box-shadow ease-in-out;

          &:hover {
            color: ${({ theme }) => theme.colorPalette.blue500};
            box-shadow: 0 2px 16px rgba(25, 68, 128, 0.1);
          }

          &.active {
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            color: ${({ theme }) => theme.colorPalette.white};

            &:hover {
              color: ${({ theme }) => theme.colorPalette.white};
            }
          }
        }
      }

      @media screen and (max-width: 800px) {
        &.tab-list {
          overflow-x: auto;
        }
      }

      @media screen and (max-width: 959px) {
        &.tab-list {
          grid-auto-flow: column;
          grid-auto-columns: minmax(160px, 1fr);
          grid-template-columns: unset;
        }
      }

      @media screen and (max-width: 567px) {
        &.tab-list {
          grid-auto-columns: minmax(140px, 1fr);
        }
      }
    }
    .tab-list {
      gap: ${({ $tabBarGutter }) => ($tabBarGutter ? `0 ${$tabBarGutter}px` : '0')};
      button.tab {
        &.active {
          color: ${({ theme }) => theme.colorPalette.blue600};

          &:hover {
            color: ${({ theme }) => theme.colorPalette.blue600};
          }
        }
      }
    }
    .ellipsis-icon {
      color: ${({ theme }) => theme.colorPalette.blue600};
    }
    &.default-variant,
    &.lifted-variant {
      .more-items-button {
        margin-left: auto;
      }
    }

    &.bordered-variant {
      .tab-list {
        button.tab {
          border-bottom: 2px solid ${({ theme }) => theme.colorPalette.gray200};

          &.active {
            border-bottom-color: ${({ theme }) => theme.colorPalette.blue600};
          }

          &:hover {
            border-bottom-color: ${({ theme }) => theme.colorPalette.blue400};
          }
        }
      }
      .ellipsis-icon {
        color: ${({ theme }) => theme.colorPalette.blue600};
      }
    }

    &.pill-variant {
      .tab-list {
        background-color: ${({ theme }) => theme.colorPalette.gray50};
        border-radius: ${({ theme }) => theme.borderRadius.md};
        padding: 6px;
        gap: 0.5rem;
        button.tab {
          padding: 2px 8px;
          border-radius: ${({ theme }) => theme.borderRadius.md};

          &:hover {
            background-color: ${({ theme }) => theme.colorPalette.gray100};
          }

          &.active {
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            color: ${({ theme }) => theme.colorPalette.white};

            &:hover {
              color: ${({ theme }) => theme.colorPalette.white};
            }
          }
        }
      }
      .more-items-button {
        margin-left: auto;
        color: ${({ theme }) => theme.colorPalette.blue500};
      }
    }

    &.lifted-variant {
      .tab-list-wrapper {
        border-bottom: 1px solid ${({ theme }) => theme.colorPalette.gray400};
        margin-bottom: 1rem;

        &::-webkit-scrollbar {
          display: none;
        }
      }
      .tab-list {
        button.tab {
          border-bottom: 1px solid transparent;
          background-color: transparent;
          border-radius: 4px 4px 0 0;
          text-align: center;

          &.active {
            background-color: transparent;
            border: 1px solid ${({ theme }) => theme.colorPalette.gray600};
            border-bottom-color: #fff;
            color: ${({ theme }) => theme.colors.foregroundActive};
          }

          &.promo-offer {
            color: ${({ theme }) => theme.colorPalette.orange500} !important;
          }
        }
      }
    }

    &:not(.pill-variant) {
      .more-items-button {
        padding: 5px 16px;
        width: 40px;
        .ellipsis-icon {
          color: ${({ theme }) => theme.colorPalette.gray500};
        }
      }
    }

    .secondary-tabs-dropdown {
      position: relative;
      top: 20px;

      button {
        padding: 4px;
        min-width: 70px;
        text-align: left;
        font-size: ${({ theme }) => theme.fontSize.sm};

        &:hover {
          background-color: ${({ theme }) => theme.colorPalette.gray100};
        }
      }
    }

    .tab-list {
      white-space: nowrap;
      display: flex;
      align-items: center;
      overflow-x: auto;

      &.overflow-shadow-left {
        -webkit-mask-image: linear-gradient(270deg, #000 85%, transparent);
        mask-image: linear-gradient(270deg, #000 85%, transparent);
      }

      &.overflow-shadow-right {
        -webkit-mask-image: linear-gradient(90deg, #000 85%, transparent);
        mask-image: linear-gradient(90deg, #000 85%, transparent);
      }

      &.overflow-shadow-both {
        --scroll-shadow-size: 20px;
        -webkit-mask-image: linear-gradient(
          to right,
          #000,
          #000,
          transparent 0,
          #000 var(--scroll-shadow-size),
          #000 calc(100% - var(--scroll-shadow-size)),
          transparent
        );
        mask-image: linear-gradient(
          to right,
          #000,
          #000,
          transparent 0,
          #000 var(--scroll-shadow-size),
          #000 calc(100% - var(--scroll-shadow-size)),
          transparent
        );
      }

      button.tab {
        cursor: pointer;
        padding: 8px 16px;
        border: 0;
        background: none;
      }
    }

    .more-items-button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 40px;
      min-width: 40px;
      color: ${({ theme }) => theme.colorPalette.gray600};
      border-bottom-color: ${({ theme }) => theme.colorPalette.gray600};

      .ellipsis-icon {
        font-size: 1.5rem;
      }
    }

    .tab-content {
      display: none;

      &.selected {
        display: block;
      }
    }

    .select-menu {
      .position-right {
        right: 5px;
        min-width: 12rem;
      }

      .select-menu-list-item {
        min-height: 35px;
        font-size: ${({ theme }) => theme.fontSize.base};
      }
    }
  }
`;

export default Tabs;
