{
  "compilerOptions": {
    "jsx": "react-jsx",
    "lib": [
      "dom",
      "esnext"
    ],
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowJs": false,
    "typeRoots": [
      "./node_modules/@types"
    ],
    "types": [
      "react",
      "react-dom"
    ],
  },
  "files": [],
  "include": [],
  "references": [
    {
      "path": "./tsconfig.lib.json"
    }
  ],
  "extends": "../../../tsconfig.base.json"
}