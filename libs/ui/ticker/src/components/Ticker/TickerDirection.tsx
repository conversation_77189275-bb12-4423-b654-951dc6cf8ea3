import React from 'react';
import { faCaretUp, faCaretDown } from '@fortawesome/pro-solid-svg-icons';
import { Icon } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';

export interface TickerDirectionProps {
  direction: 'up' | 'down';
}

export const TickerDirection: React.FC<TickerDirectionProps> = ({ direction }) => {
  return (
    <TickerDirectionWrapper className="direction" direction={direction}>
      {direction === 'up' ? <Icon icon={faCaretUp} /> : <Icon icon={faCaretDown} />}
    </TickerDirectionWrapper>
  );
};

export const TickerDirectionWrapper = styled.div<TickerDirectionProps>`
  color: ${({ direction, theme }) => (direction === 'up' ? theme.colorPalette.green500 : theme.colorPalette.red500)};
  padding-bottom: 0.5rem;
`;
