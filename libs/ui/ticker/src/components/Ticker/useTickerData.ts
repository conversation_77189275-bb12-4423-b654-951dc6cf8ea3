import React, { startTransition } from 'react';
import { SessionContext } from '@benzinga/session-context';
import { CryptoManager } from '@benzinga/crypto-manager';
import { DelayedQuote, QuotesManager, QuoteFeedRestfulEvent } from '@benzinga/quotes-manager';
import Hooks from '@benzinga/hooks';
import { Subscribable } from '@benzinga/subscribable';

export interface CryptoQuote {
  change: number;
  changePercent: number;
  dayHigh: number;
  dayLow: number;
  marketCap: string | number;
  name: string;
  price: number;
  symbol: string;
  volume: number;
}

export interface UseTickerDataResult {
  stockQuote?: DelayedQuote | undefined;
  cryptoQuote?: CryptoQuote | undefined;
}

export const useTickerData = (symbol: string, exchange?: string): UseTickerDataResult => {
  const session = React.useContext(SessionContext);
  const [stockQuote, setStockQuote] = React.useState<DelayedQuote>();
  const [cryptoQuote, setCryptoQuote] = React.useState<CryptoQuote>();
  const [isSubscribed, setIsSubscribed] = React.useState<{ [symbol: string]: boolean } | null>(null);

  const cryptoManager = session.getManager(CryptoManager);
  const quotesManager = session.getManager(QuotesManager);

  React.useEffect(() => {
    if (symbol) {
      const _symbol = symbol?.split('/USD')[0];
      if (symbol?.includes('/USD')) {
        cryptoManager.getCoin(_symbol).then(response => {
          const coin = response.ok;
          if (coin) {
            const data = {
              change: coin.price_change_24h,
              changePercent: coin.price_change_percentage_24h,
              dayHigh: coin.high_24h,
              dayLow: coin.low_24h,
              marketCap: coin.market_cap,
              name: coin.name,
              price: coin.current_price,
              symbol: symbol.toUpperCase(),
              volume: coin.total_volume,
            };
            startTransition(() => {
              setCryptoQuote(data);
              setStockQuote(undefined);
            });
          }
        });
      } else {
        const stock = symbol;
        if (!isSubscribed || (isSubscribed && !isSubscribed[stock])) {
          quotesManager.addSymbolSubscription(stock);
          setIsSubscribed({ ...isSubscribed, [stock]: true });
        } else if (isSubscribed && isSubscribed[stock]) {
          const newStockQuote = quotesManager.getQuoteFromSubscriptionStore(stock);
          if (newStockQuote) {
            startTransition(() => {
              setStockQuote(newStockQuote);
            });
          }
        }
      }
    }
    return () => quotesManager.removeSymbolSubscription(symbol);
  }, [exchange, symbol, cryptoManager, quotesManager, isSubscribed]);

  Hooks.useSubscriber(quotesManager as Subscribable<QuoteFeedRestfulEvent>, (event: QuoteFeedRestfulEvent) => {
    if (event.type === 'quote:delayed_quote_update' && event.symbol === symbol) {
      startTransition(() => {
        setStockQuote(event.quote);
      });
    }
  });

  return { cryptoQuote, stockQuote };
};
