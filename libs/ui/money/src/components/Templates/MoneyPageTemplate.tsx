'use client';
import React, { Suspense } from 'react';
import styled from '@benzinga/themetron';
import { showCornerStone } from '@benzinga/navigation-ui';
import { MoneyTemplateProps } from '../../entities';
import { MoneyPostLayout } from '../MoneyPostLayout';
import { Tabs } from '@benzinga/core-ui';
import { useCustomRouter } from '@benzinga/hooks';
import { usePathname } from 'next/navigation';
import { PageCampaigns } from '../Campaign/PageCampaigns';

export const MoneyPageTemplate: React.FC<MoneyTemplateProps> = ({
  layoutAboveArticle,
  layoutAboveSidebar,
  layoutBelowArticle,
  layoutFooter,
  layoutHeader,
  layoutTabs,
  layoutTabsOptions = {},
  post,
  tabOptions = {},
  tabsTitle,
  width,
}) => {
  const router = useCustomRouter();
  const pathname = usePathname();

  React.useEffect(() => {
    if (Array.isArray(layoutTabs)) {
      if (layoutTabsOptions?.prefetchAllTabs) {
        layoutTabs
          .filter(tab => tab.key !== pathname)
          .forEach(tab => {
            router.prefetch(tab.key);
          });
      } else if (Array.isArray(layoutTabsOptions?.prefetchTabs)) {
        layoutTabsOptions.prefetchTabs
          .filter(tab => tab !== pathname)
          .forEach(tab => {
            router.prefetch(tab);
          });
      }
    }
  }, [layoutTabs, router, layoutTabsOptions, pathname]);

  // TODO: PROBABLY A BETTER WAY TO DO THIS
  const pageType = post?.taxonomies?.find(
    object => object.taxonomy === 'page-type' && object.term?.slug === 'full-width',
  )?.term?.slug;
  const pageWidth = width || showCornerStone(post ?? null) || pageType === 'full-width' ? 'wide' : 'narrow';

  const onChangeTab = (key: string) => {
    window.location.replace(key);
    //router.replace(key);
  };

  return (
    <PageTemplateWrapper className={`money-page-template ${pageWidth}`}>
      {layoutTabs && (
        <div className="w-full flex items-center justify-center px-2 tab-header">
          <div className="layout-container max-w-[1300px] w-full">
            {tabsTitle && <h1 className="text-3xl uppercase mb-4">{tabsTitle}</h1>}
            <Tabs
              activeTab={pathname as string}
              isShowMoreButtonEnabled={false}
              onChange={onChangeTab}
              tabBarGutter={10}
              tabs={layoutTabs}
              variant="lifted"
              {...tabOptions}
            />
          </div>
        </div>
      )}
      {layoutHeader}
      <MoneyPostLayout
        layoutAboveArticle={layoutAboveArticle}
        layoutAboveSidebar={layoutAboveSidebar}
        layoutBelowArticle={layoutBelowArticle}
        post={post ?? null}
      />
      {layoutFooter && (
        <div className="layout-footer primary">
          <div className="layout-container mx-auto">{layoutFooter}</div>
        </div>
      )}
      <Suspense fallback={<div />}>
        <PageCampaigns post={post} />
      </Suspense>
    </PageTemplateWrapper>
  );
};

export default MoneyPageTemplate;

const PageTemplateWrapper = styled.div`
  &.money-page-template {
    &.narrow {
      .header-container,
      .layout-container {
        max-width: 1100px;
      }
    }
    .layout-footer {
      .layout-container {
        max-width: 1100px;
      }
      &.primary {
        padding-top: 1rem;
        background-color: rgb(242, 248, 255);
        border-top: 1px solid rgb(184, 203, 230);
      }
    }

    .tabs-container.lifted-variant {
      .tab-list-wrapper {
        margin-bottom: unset;
        border-bottom: unset;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        padding-right: 5px;

        @media screen and (min-width: 1120px) {
          padding-right: 0;
        }

        .tab-list {
          .tab {
            padding: 10px 40px 9px;
            font-weight: 600;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            background-color: transparent;
            border: 1px solid ${({ theme }) => theme.colorPalette.gray300};
            border-bottom: none;

            &.active {
              background: #fff;
              border-color: #f0f0f0;
              border-bottom-color: #fff;
              color: ${({ theme }) => theme.colorPalette.blue500};
            }
          }
        }
      }
    }

    .tab-header {
      background: radial-gradient(1130.97% 126.55% at 78.68% -23.26%, #ffffff 0%, #ecf1f9 80%, #e3eefe 100%);
      padding-top: 1.5rem;
    }

    .money-page-main {
      /* display: flex; */
      position: relative;
      width: 100%;

      @media (max-width: 1119px) {
        padding: 0 0.75rem;
      }
      .money-post-wrapper {
        width: 100%;
      }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      &.section-title {
        margin-top: 2rem;
        margin-bottom: 1rem;
      }
    }

    .cornerstone-navigation {
      display: block;
      width: 100%;
      border: 1px solid #ceddf2;
      border-radius: 4px;
      padding: 0;
    }
  }
`;
