import React, { useEffect } from 'react';
import styled from '@benzinga/themetron';
import { BzImage } from '@benzinga/image';
import { Candle } from '@benzinga/chart-manager';
import { generateChartOption, getCandleColor } from './utils';
import { formatPercentage, formatPriceWithCurrency } from '@benzinga/utils';
import { DetailsQuoteType, RankingDetail } from '@benzinga/quotes-manager';
import { AddToWatchlist } from '@benzinga/watchlist-ui';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';
import { EdgeRanking } from '@benzinga/ticker-ui';
import { usePermission } from '@benzinga/user-context';

const EChart = React.lazy(() => import('./EChart'));

interface StockInfoProps extends StockInfoHeaderProps {
  candles: Candle[];
  rankingData?: RankingDetail | null;
}

interface StockInfoHeaderProps {
  symbol: string;
  name: string | null;
  price: number | null;
  changePercent: number | null;
  logoUrl: string | null;
  type: DetailsQuoteType;
}

export const StockInfoChart: React.FC<StockInfoProps> = ({
  candles,
  changePercent,
  logoUrl,
  name,
  price,
  rankingData,
  symbol,
  type,
}) => {
  const chartColor = getCandleColor(candles[candles.length - 2], candles[candles.length - 1]);
  const chartOption = generateChartOption(candles, chartColor);
  const [show, setShow] = React.useState(false);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');

  useEffect(() => {
    if (hasPermission) {
      setShow(true);
    }
  }, [hasPermission]);

  return (
    <Container className="flex flex-col">
      <StockInfoHeader
        changePercent={changePercent}
        logoUrl={logoUrl}
        name={name}
        price={price}
        symbol={symbol}
        type={type}
      />
      <Divider />
      {/* {(type === 'CRYPTO' || !rankingData?.exists) && (
        <div className="w-full h-[100px]">
          <React.Suspense fallback={<div />}>
            <EChart customOptions={chartOption} />
          </React.Suspense>
        </div>
      )} */}
      {rankingData && rankingData.exists && (
        <EdgeRanking {...rankingData} adType={'stockwidget'} showRanking={show} symbol={symbol} variant="dark" />
      )}
    </Container>
  );
};

const Divider: React.FC = () => {
  return <div className="border-t border-[#192940] my-4" />;
};

export const StockInfoHeader: React.FC<StockInfoHeaderProps> = ({
  changePercent,
  logoUrl,
  name,
  price,
  symbol,
  type,
}) => {
  const { t } = useTranslation('quote', { i18n });
  const changePercentClassname =
    Number(changePercent) === 0
      ? 'bg-[#273851] text-white'
      : Number(changePercent) > 0
        ? 'bg-[#143233] text-green-500'
        : 'bg-[#432734] text-red-500';
  return (
    <a className="stock-info-header flex" href={`/quote/${symbol}`} rel="noopener noreferrer" target="_blank">
      {logoUrl && (
        <div className="flex justify-center items-center border-2 border-gray-700 rounded-lg p-1 mr-4 bg-white">
          <BzImage
            alt={`${symbol} Logo`}
            height={40}
            layout="fixed"
            loader={() => {
              return logoUrl;
            }}
            objectFit="contain"
            src={logoUrl}
            width={40}
          />
        </div>
      )}
      <TickerPriceDetail className="flex items-center justify-between gap-2 w-full">
        <div className="flex flex-col">
          <span className="stock-info-symbol text-white">
            {type === 'CRYPTO' && '$'}
            {symbol?.replace('/USD', '')}
          </span>
          <span className="stock-info-name text-gray-500 truncate max-w-[160px] sm:max-w-[200px]">{name}</span>
        </div>
        <div className="flex flex-row gap-3 items-center">
          <div className="flex flex-col items-end">
            <span className="text-white text-right">
              {price ? formatPriceWithCurrency(price, 'USD') : 'Not Available'}
            </span>
            <span
              className={`change-percent ${changePercentClassname} font-bold text-center text-xs rounded-full px-2 py-1 mt-0.5`}
            >
              {changePercent ? `${formatPercentage(changePercent)}` : '-'}%
            </span>
          </div>
          <div className="quick-add-watchlist">
            <AddToWatchlist buttonVariant="flat-blue" showIcon={true} symbol={symbol} variant="compact" />
          </div>
        </div>
      </TickerPriceDetail>
    </a>
  );
};

export default StockInfoChart;

const Container = styled.div``;

const TickerPriceDetail = styled.div`
  .quick-add-watchlist {
    button {
      min-width: 40px;
      min-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center !important;
      svg {
        width: 1.6em !important;
        height: 1.6em !important;
      }
    }
  }
`;
