import DomPurify from 'isomorphic-dompurify';

export const sanitizeHTML = (html: string): string => DomPurify.sanitize(html, { ADD_TAGS: ['amp'] });

export const parseToHTML = (html: string): string => {
  if (!html) {
    return '';
  }
  try {
    const domParser = new DOMParser();
    const doc = domParser.parseFromString(html, 'text/html');
    return doc.body.textContent?.trim() ?? '';
  } catch (error) {
    return '';
  }
};
