import { runningClientSide } from '@benzinga/utils';
import { GeoData, GoLinkData, Referrer } from './entities';
import { SessionPropsExtension } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';

const OVERRIDDEN_REF_KEY = 'overriddenReferrer';

declare const global: {
  geo: GeoData;
};

export const getDocumentReferrer = (): Referrer => {
  if (!runningClientSide()) {
    return '';
  }

  return window?.[OVERRIDDEN_REF_KEY] || document?.referrer;
};

export const setNewDocumentReferrer = (referrer: Referrer): void => {
  if (!runningClientSide()) {
    return;
  }

  if (typeof window !== 'undefined') {
    window[OVERRIDDEN_REF_KEY] = referrer;
  }
};

export const extractGoLinkDataFromAnchor = (data: GoLinkData, anchor: HTMLAnchorElement): GoLinkData => {
  const event_action: string | undefined = anchor.getAttribute('data-action') ?? undefined;
  const event_category: string | undefined = anchor.getAttribute('data-category') ?? 'Lead Click';
  const event_label: string | undefined = anchor.getAttribute('data-label') ?? 'Page Content Link';
  const partner_slug = anchor.getAttribute('data-partner') ?? undefined;
  const placement_id = anchor.getAttribute('placement-id') ?? undefined;
  const link = anchor.getAttribute('data-link') ?? undefined;
  const author_html = document.querySelectorAll('.author-name')?.[0]?.innerHTML ?? undefined;
  const text = anchor?.innerText;
  return { ...data, author_html, event_action, event_category, event_label, link, partner_slug, placement_id, text };
};

export const url_data = (key: string | null = null) => {
  const urlSearchParams: any = new URLSearchParams(window.location.search) as any;
  const data: any = Object.fromEntries(urlSearchParams.entries());
  const matches = window.location.pathname.match('[A-z0-9-]+/[0-9]+/[0-9]+/([0-9]+)/([A-z0-9-]+)');
  data['path'] = window.location.pathname;
  if (matches && matches[1]) {
    data['node_id'] = matches[1];
    data['node_slug'] = matches[2];
  }
  return key ? data[key] : data;
};

const generateGoLinkAction = (data: GoLinkData) => {
  let action = '';
  if (data.partner && data.name && data.name !== data.partner)
    action = '::Partner:: `' + data.partner + '` ::GoLink:: `' + data.name + '`';
  else if (data.partner) action = '::Partner:: `' + data.partner + '`';
  else if (data.name) action = '::GoLink:: `' + data.name + '`';
  if (data.text) action += ` ::Text:: ${data.text}`;
  return action;
};

const buildTrackedUrl = (go_link_data: GoLinkData) => {
  const author_name = go_link_data.author_html ? go_link_data.author_html.trim() : null;

  let href = go_link_data.href;
  if (go_link_data.partner_slug && !href) {
    href = `${window.location.protocol}//${window.location.host}/go/${go_link_data.partner_slug}`;
  } else if (go_link_data.link && !href) {
    href = go_link_data.link;
  }

  const params = url_data();

  if (go_link_data?.post_id) params['page_r'] = go_link_data?.post_id;
  if (go_link_data.placement_id) {
    params['pi'] = go_link_data.placement_id;
  }

  const referrer = getDocumentReferrer();
  if (referrer) {
    params['referrer'] = referrer;
  }

  if (go_link_data.event_action) {
    params['event_action'] = go_link_data.event_action;
  }
  if (go_link_data.event_category) {
    params['event_category'] = go_link_data.event_category;
  }
  if (go_link_data.event_label) {
    params['pl'] = go_link_data.event_label;
    params['event_label'] = go_link_data.event_label;
  }
  if (global.geo) params['cc'] = global.geo.country_code;
  if (global.geo?.region) params['region'] = global.geo.region;
  if (global.geo?.city) params['city'] = global.geo.city;
  if (author_name) params['an'] = author_name;
  params['tid'] = (Math.random() + 1).toString(36).substring(2);

  if (window.location.search !== '') {
    const utm_source = new URLSearchParams(window.location.search).get('utm_source');
    if (utm_source && utm_source !== '') {
      params['utm_source'] = utm_source;
    }
  }

  if (!href) return;

  return buildUrl(href, params);
};

const buildUrl = (href: string, params: any) => {
  const parser = document.createElement('a');
  parser.href = href;

  const esc = encodeURIComponent;
  const query = Object.keys(params)
    .map(k => esc(k) + '=' + esc(params[k]))
    .join('&');

  return parser.search.length ? parser.href + '&' + query : parser.href + '?' + query;
};

export const openLink = (go_link_data: GoLinkData, session: SessionPropsExtension) => {
  const trackedUrl = buildTrackedUrl(go_link_data);

  if (!trackedUrl) return;

  const matches = trackedUrl.match(/\/go\/([\w\d-]+)/);
  if (trackedUrl && matches && matches[1]) {
    // Open Url
    window.open(trackedUrl);

    // Send Event
    const category = go_link_data.event_category ?? 'Lead Click';
    const action = go_link_data.event_action ?? generateGoLinkAction(go_link_data);
    const label = go_link_data.event_label ?? 'Page Content Link';
    session.getManager(TrackingManager).trackLinkEvent('click', {
      link_action: action,
      link_id: label,
      link_type: category,
      link_url: trackedUrl,
      value: go_link_data.text || undefined,
    });

    return true;
  }

  return false;
};

export const extractUtmParamsFromUrl = (urlString: string): Record<string, string> => {
  if (!runningClientSide()) return {};

  const url = new URL(urlString);
  const urlParams = url.searchParams;
  const utmParams: Record<string, string> = {};

  urlParams.forEach((value, key) => {
    if (key.startsWith('utm_') && value) {
      utmParams[key.replace('utm_', '')] = value;
    }
  });

  return utmParams;
};
