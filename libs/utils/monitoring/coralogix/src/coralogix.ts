'use client';

import { User } from '@benzinga/session';
import { CoralogixRum } from '@coralogix/browser';

const IGNORED_ERRORS = [
  // 1password can cause this error https://1password.community/discussion/139709/latest-chrome-extension-breaks-google-sign-in-on-our-website
  '[GSI_LOGGER]: Check credential status returns invalid response.',
  '[GSI_LOGGER]: The given origin is not allowed for the given client ID.',
  '[GSI_LOGGER]: FedCM get() rejects with NetworkError: Error retrieving a token.',
  'Warning: Intercom was not initialized. Please turn AdBlock off if you have it enabled.',
  "undefined is not an object (evaluating /'v[e].loading=!1/')",
  'ResizeObserver loop completed with undelivered notifications.',
  "unhandledError::1:447118@https://vidstat.taboola.com/vpaid/units/33_9_6/infra/cmTagFEED_MANAGER.js::Cannot read properties of null (reading 'querySelector')",
  'Script error.',
  'The request is not allowed by the user agent or the platform in the current context, possibly because the user denied permission.',
  'There has been a problem with your fetch operation:  Load failed',
  'The play() request was',
  'UID2 shared library - ',
  'e.getUserIdsAsEids is not a function',
  'window.CONFIG_JS_CALLBACK is not a function',
  't.match is not a function',
  '_match_node_text_recursive is not defined',
  'fbq is not defined',
  'jQuery is not defined',
  "__gpp.events[i].splice is not a function. (In '__gpp.events[i].splice(i,1)', '__gpp.events[i].splice' is undefined)",
  'Blocked a frame with origin "https://www.benzinga.com" from accessing a cross-origin frame',
  '[GPT] Attempted to collect prebid data but window.pbjs is undefined. https://goo.gle/gpt-message#126',
  'Exception in queued GPT command Invalid array length',
  'Exception in queued GPT command t.googleTagSlot is null',
  'NetworkError when attempting to fetch resource',
  'Failed to import script',
  'Failed to inject Raptive script',
  'Failed to inject Nativo script',
  'WCT Server Unavailable',
  'Connection to web push daemon failed',
  'The source window of session client post messages cannot be changed from the source of the first message.',
  'detectIncognito somehow failed to query storage quota: Unknown error.',
  'detectIncognito cannot determine the browser',
  'Failed to fetch',
  'Failed to load',
  'Button config is missing',
  'The operation was aborted',
  'skipping pixel true false',
  'Logger send request failed',
  'Registration failed - permission denied',
  'XMLHttpRequest Error: Network request failed',
  'Network Error',
  'Load failed',
  'Invalid string length',
  "'' is not a valid selector.",
  'status -> 403',
  'Request failed with status code 401',
  'Minified React error #418;',
  'Minified React error #421;',
  'Minified React error #422;',
  'Minified React error #425;',
  '{}',
  'N/A',
  'JSON.stringify cannot serialize cyclic structures.',
  /.*Failed to read a named property*/,
  /.*Failed to execute 'insertBefore' on 'Node'.*/,
  /.*%cClsDisableAds::checkCommandQueue color: #999;*/,
  /.*Exception in googletag.cmd function*/,
  /.*Exception in queued GPT command null is not an object (evaluating 'c.googleTagSlot.setTargeting')*/,
  /.*Cannot read properties of undefined (reading 'width')*/,
  /.*Cannot set properties of undefined (setting 'enableEmbed')*/,
  /.*Cannot read properties of null (reading 'setTargeting')*/,
  /.*vidstat\.taboola\.com*./,
  /.*sync\.taboola\.com*./,
  /.*cdn\.taboola\.com*./,
  /.*contextweb\.com.*/,
  /.*gum\.criteo\.com*/,
  /.*script: https:\/\/bh\.contextweb\.com\/bh\/rtset.*/,
  /Loading chunk \d* failed/,
  /Loading CSS chunk \d* failed/,
  /.*Converting circular structure to JSON.*AdThrive.*/,
  /ERROR BOUNDARY.*"error": \{\}/,
];

const IGNORED_URLS = [
  // Ignore all non-benzinga URLs
  /^(?!https?:\/\/([a-zA-Z0-9-]+\.)*benzinga\.com(?=\/|:|$)).+$/,
  // Still specifically include known problematic benzinga URLs
  /^https:\/\/www\.benzinga\.com\/_next\/data\/.*\.json$/,
  // Explicitly ignore some problematic third-party URLs that might include benzinga.com in query params
  /^https:\/\/ups\.analytics\.yahoo\.com/,
];

// https://coralogix.com/docs/rum-integration-package/
// https://coralogix.com/docs/real-user-monitoring/
// https://www.npmjs.com/package/@coralogix/browser
export const CoralogixRumLoader = (user: User | undefined) => {
  if (process.env.NODE_ENV !== 'production') return;
  CoralogixRum.init({
    application: process.env.CORALOGIX_RUM_APPLICATION,
    // beforeSend: event => {
    //   if (event.environment !== 'production') {
    //     return null;
    //   }
    //   return event;
    // },
    coralogixDomain: process.env.CORALOGIX_RUM_DOMAIN,
    environment: process.env.NODE_ENV || 'staging',
    ignoreErrors: IGNORED_ERRORS,
    ignoreUrls: IGNORED_URLS,
    public_key: process.env.CORALOGIX_RUM_PUBLIC_KEY,
    sessionSampleRate: 0.5,
    user_context: user
      ? {
          user_email: user.email,
          user_id: String(user.benzingaUid),
          user_metadata: {
            access_type: user.accessType,
          },
          user_name: user.displayName,
        }
      : undefined,
    // Percentage of overall sessions being tracked, defaults to 100%
    version: process.env.RELEASE_VERSION || '',
    // sessionRecordingConfig: {
    //   enable: true,
    //   autoStartSessionRecording: true, // Automatically records your session when SDK is up.
    //   recordConsoleEvents: true, // Will record all console events from dev tools. Levels: log, debug, warn, error, info, table etc..
    //   sessionRecordingSampleRate: 1, // Percentage of overall sessions recording being tracked, defaults to 100% and applied after the overall sessionSampleRate.
    //   blockClass: 'rr-block', // Use a string or RegExp to redact all elements that contain this class, defaults to rr-block.
    //   ignoreClass: 'rr-ignore', // Use a string or RegExp to Ignore all events that contain this class, defaults to rr-ignore.
    //   maskTextClass: 'rr-mask', // Use a string or RegExp to mask all elements that contain this class, defaults to rr-mask.
    //   maskAllInputs: false, // Mask all input content as * (Default false), refer to Input types.
    //   maskInputOptions: { password: true }, // Mask some kinds of input as *, By Default the SDK masking password inputs.
    // },
  });
};
