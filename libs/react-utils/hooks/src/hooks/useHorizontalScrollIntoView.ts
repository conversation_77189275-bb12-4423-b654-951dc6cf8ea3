'use client';

import React from 'react';

export const useHorizontalScrollIntoView = (elementId: string) => {
  const elementRef = React.useRef<HTMLElement | null>(null);

  React.useEffect(() => {
    elementRef.current = document.getElementById(elementId);
  }, [elementId]);

  const scrollToElement = () => {
    if (elementRef.current) {
      const container = elementRef.current.parentElement;
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = elementRef.current.getBoundingClientRect();

        const scrollLeft = elementRect.left - containerRect.left - (containerRect.width - elementRect.width) / 2;

        container.scrollTo({
          behavior: 'smooth',
          left: container.scrollLeft + scrollLeft,
        });
      }
    }
  };

  return scrollToElement;
};

export default useHorizontalScrollIntoView;
