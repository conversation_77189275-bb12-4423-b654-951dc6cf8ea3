'use client';
import React, { startTransition } from 'react';

import Hooks from '@benzinga/hooks';
import { SessionContext } from '@benzinga/session-context';
import { UserSubscription } from '@benzinga/session';
import { SubscriptionsManager, SubscriptionsManagerEvent } from '@benzinga/subscription-manager';

/**
 * Hook to check which subscriptions a user has
 */
export const useUserSubscriptions = (): UserSubscription[] | undefined => {
  const session = React.useContext(SessionContext);
  const subscriptionManager = session.getManager(SubscriptionsManager);
  const [subscription, setSubscription] = React.useState(() => subscriptionManager.getSubscriptionsStored());

  Hooks.useSubscriber(subscriptionManager, (event: SubscriptionsManagerEvent) => {
    switch (event.type) {
      case 'subscription:user_subscriptions_changed':
        startTransition(() => {
          setSubscription(event.userSubscriptions);
        });
        break;
    }
  });

  React.useEffect(() => {
    if (session.session?.getAuthenticationManager().isLoggedIn()) {
      session.session
        ?.getAuthenticationManager()
        .getAuthSession()
        .then(session => {
          startTransition(() => {
            if (session?.ok?.user?.subscriptions) {
              setSubscription(session.ok.user.subscriptions);
            }
          });
        });
    } else {
      subscriptionManager.getSubscriptions().then(s => startTransition(() => setSubscription(s.ok)));
    }
  }, [subscriptionManager, session]);

  return subscription;
};
