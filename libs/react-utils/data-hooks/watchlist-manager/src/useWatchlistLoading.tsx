'use client';
import React, { startTransition } from 'react';
import { Watchlist, WatchlistManager, WatchlistManagerEvent } from '@benzinga/watchlist-manager';
import { SessionContext } from '@benzinga/session-context';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import Hooks from '@benzinga/hooks';
import { UserManager, UserManagerEvent } from '@benzinga/user-manager';

export const useWatchlistsLoading = () => {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [watchlistSymbols, setWatchlistSymbols] = React.useState<string[]>([]);
  const [watchlists, setWatchlists] = React.useState<Watchlist[]>([]);
  const session = React.useContext(SessionContext);

  const userManager = session.getManager(UserManager);
  const watchlistManager = session.getManager(WatchlistManager);

  const isLoggedIn = useIsUserLoggedIn();

  const loadWatchlists = React.useCallback(() => {
    (async () => {
      const response = await watchlistManager.getWatchlists();
      if (response.err) {
        setIsLoading(false);
      } else if (response.ok && Array.isArray(response.ok)) {
        setWatchlists(response.ok);
        setIsLoading(false);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Load Locally Stored Symbols for Speed
  React.useEffect(() => {
    const storedSymbols = localStorage.getItem('WATCHLIST_SYMBOLS');
    if (storedSymbols) {
      const symbols = storedSymbols.split(',');
      setWatchlistSymbols(symbols);
    }
  }, []);

  // Update Watchlists on User Update
  Hooks.useSubscriber(userManager, (event: UserManagerEvent) => {
    if (event.type === 'user:user_update') {
      setTimeout(() => {
        loadWatchlists();
      });
    }
  });

  const handleSetWatchlistSymbols = React.useCallback((watchlists: Watchlist[]) => {
    const symbols: string[] = [];
    watchlists.forEach(portfolio => {
      if (symbols.length > 50) return;
      Array.isArray(portfolio?.symbols) &&
        portfolio.symbols.forEach(item => {
          symbols.push(item.symbol);
        });
    });
    const tickers = Array.from(new Set(symbols));
    localStorage.setItem('WATCHLIST_SYMBOLS', tickers.join(','));
    startTransition(() => {
      setWatchlistSymbols(tickers);
    });
  }, []);

  // Update Symbols on Watchlist Update
  Hooks.useSubscriber(watchlistManager, (event: WatchlistManagerEvent) => {
    if (event.type === 'watchlist:updated_watchlists') {
      setTimeout(() => {
        handleSetWatchlistSymbols(event.watchlists);
      });
    }
  });

  return {
    isLoading,
    isLoggedIn,
    watchlistSymbols,
    watchlists,
  };
};
