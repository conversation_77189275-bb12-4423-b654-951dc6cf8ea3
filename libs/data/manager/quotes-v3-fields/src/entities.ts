import { Session } from '@benzinga/session';

export interface FullDataField extends Omit<DataField, 'format'> {
  format: 'watchlist' | DataField['format'];
  filterConvert?: (filter: FilterObject, session: Session) => FilterObject;
}

export interface FilterObject {
  field: string;
  isCustom?: boolean;
  operator: string;
  parameters: string[];
}

export enum TargetSource {
  All = 'all',
  Stocks = 'stocks',
  Crypto = 'crypto',
}

export type DataField = {
  category: string;
  columnable: boolean;
  description: string;
  options?: string;
  filterable: boolean;
  format:
    | 'boolean'
    | 'flag'
    | 'link'
    | 'note'
    | 'period'
    | 'priceAlert'
    | 'progress'
    | 'set'
    | 'sparkLine'
    | 'symbol'
    | 'text'
    | 'calculatedChangePercent'
    | 'change'
    | 'changePercent'
    | 'changeShortenedNumber'
    | 'commaSeparatedNumber'
    | 'date'
    | 'dateTime'
    | 'number'
    | 'positiveNumber'
    | 'percentNumber'
    | 'positivePercentNumber'
    | 'positivePrice'
    | 'price'
    | 'shortenedNumber'
    | 'percentNumberBackgroundColor'
    | 'time';
  label: string;
  labelShort: string;
  name: string;
  permissions: string;
  permissionsCom: string;
  updateFrequency: string;
  filterOptions?: Record<string, string> | null | undefined;
  sourceSupport?: TargetSource;
};

export type DataFieldIngress = {
  quoteFields: Omit<DataField, 'filterOptions'>[] & { options: string | null | undefined };
  quoteFieldOptions: Record<string, Record<string, string>>;
};
