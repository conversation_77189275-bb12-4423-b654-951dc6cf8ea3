'use client';
import { Input, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import SelectFilter from './SelectFilter';
import WatchlistFilter from './WatchlistFilter';
import { RangeInput } from './RangeInput';
import styled, { css } from '@benzinga/themetron';
import { Lock } from '@benzinga/themed-icons';
import { FullDataField, FilterObject } from '@benzinga/quotes-v3-fields-manager';
import { Tooltip } from 'antd';
import { PermissionedComponent } from '@benzinga/user-context';
import { Permission } from '@benzinga/session';
import { Warning as WarningIcon } from '@benzinga/valentyn-icons';

export const isRangeFormat = (field?: FullDataField) => {
  switch (field?.format) {
    case 'calculatedChangePercent':
    case 'change':
    case 'changePercent':
    case 'changeShortenedNumber':
    case 'commaSeparatedNumber':
    case 'date':
    case 'dateTime':
    case 'percentNumberBackgroundColor':
    case 'number':
    case 'positiveNumber':
    case 'percentNumber':
    case 'positivePercentNumber':
    case 'positivePrice':
    case 'price':
    case 'shortenedNumber':
    case 'time':
      return true;
    case 'boolean':
    case 'flag':
    case 'link':
    case 'note':
    case 'period':
    case 'priceAlert':
    case 'progress':
    case 'set':
    case 'sparkLine':
    case 'symbol':
    case 'text':
    default:
      return false;
  }
};

function f(field: FullDataField, filter?: FilterObject): FilterObject {
  if (!filter) {
    if (isRangeFormat(field)) {
      return { field: field.name ?? '', operator: 'bt', parameters: [] };
    } else {
      return { field: field.name ?? '', operator: 'in', parameters: [] };
    }
  }

  return filter;
}

//Operator must be one of UNKNOWN_OPERATOR, LT, GT, GTE, LTE, EQ, NE, IN, BETWEEN, REGEEX, NOTIN, BEGINSWITH, ENDSWITH, CONTAINS, NOTEMPTY, ISEMPTY, UNRECOGNIZED
const FilterComponent: React.FC<{
  disabled?: boolean;
  field: FullDataField;
  filter: FilterObject;
  onChange: (filter: FilterObject) => void;
  isScreener?: boolean;
}> = ({ disabled, field, filter, isScreener, onChange }) => {
  const textOperatorOptions = React.useMemo(
    () => [
      { label: 'Contains', value: 'contains' },
      { label: 'Equals', value: 'eq' },
      { label: 'Begins With', value: 'beginsWith' },
      { label: 'Ends With', value: 'endsWith' },
    ],
    [],
  );

  switch (field.format) {
    case 'boolean':
      return <SelectFilter disabled={disabled} filter={filter} multiple={false} onChange={onChange} />;
    case 'text':
    case 'flag':
    case 'link':
    case 'note':
    case 'period':
      if (field.filterOptions) {
        return <SelectFilter disabled={disabled} filter={filter} multiple={true} onChange={onChange} />;
      } else {
        if (!textOperatorOptions.some(({ value }) => value === filter.operator)) {
          onChange({ field: field.name, operator: textOperatorOptions[0].value, parameters: filter.parameters });
        }
        const before = (
          <StyledSelect
            disabled={disabled}
            labelInValue
            onChange={option =>
              onChange({
                field: field.name,
                operator:
                  option && typeof option === 'object' && 'value' in option
                    ? String(option.value)
                    : textOperatorOptions[0].value,
                parameters: filter.parameters,
              })
            }
            value={filter.operator}
          >
            {textOperatorOptions.map(({ label, value }) => (
              <Select.Option key={value} value={value}>
                {label}
              </Select.Option>
            ))}
          </StyledSelect>
        );
        return (
          <Input
            addonBefore={before}
            disabled={disabled}
            onChange={e => onChange({ field: field.name, operator: filter.operator, parameters: [e.target.value] })}
            size="small"
            value={filter.parameters?.[0]}
          />
        );
      }

    case 'calculatedChangePercent':
    case 'change':
    case 'changePercent':
    case 'changeShortenedNumber':
    case 'commaSeparatedNumber':
    case 'date':
    case 'dateTime':
    case 'number':
    case 'positiveNumber':
    case 'percentNumber':
    case 'positivePercentNumber':
    case 'positivePrice':
    case 'percentNumberBackgroundColor':
    case 'price':
    case 'shortenedNumber':
    case 'time':
      return <RangeInput disabled={disabled} filter={filter} isScreener={isScreener} onChange={onChange} />;
    case 'watchlist':
      return <WatchlistFilter disabled={disabled} filter={filter} onChange={onChange} />;

    default:
      return <div />;
  }
};

/**
 * A component that contains a labels, tool icons (top right), and the field specific input.
 */
export const FilterBox: React.FC<{
  dataField: FullDataField;
  disabled?: boolean;
  filter?: FilterObject;
  isMillerColumns?: boolean;
  isSingleNewsfeedField?: boolean;
  onChange: (filter: FilterObject) => void;
  permission?: Permission;
  tools?: (null | React.ReactElement)[];
  isScreener?: boolean;
}> = props => {
  const field = props.dataField;
  const [filter, setFilter] = useState(() => f(field, props.filter));
  const input = (
    <FilterComponent
      disabled={props.disabled}
      field={field}
      filter={filter}
      isScreener={props.isScreener}
      onChange={props.onChange}
    />
  );
  useEffect(() => {
    setFilter(f(field, props.filter));
  }, [field, props.filter]);

  const activeClass = props.filter?.parameters && props.filter.parameters.length > 0 ? 'active' : '';
  return (
    <PermissionedComponent
      doNotDisplayPrompt={!props.isScreener ? false : true}
      doNotStopPropagation={!props.isScreener ? false : true}
      permission={props.permission ?? { action: 'bzpro/scanner/open', resource: '#' }}
    >
      {access => (
        <Container className={activeClass} isMillerColumn={props.isMillerColumns} key={field.name as string}>
          {props.isSingleNewsfeedField && (
            <Label>
              <Qtip
                overlayStyle={{ opacity: '1' }}
                title={
                  <TipInfo>
                    <b>{field.label}</b>
                    {!access && <Locked />}
                    <p>{field.description}</p>
                  </TipInfo>
                }
              >
                <LabelText>
                  {props.disabled && <Warning />}
                  <Text>{field.label}</Text>
                </LabelText>
              </Qtip>

              <StyledInput>{input}</StyledInput>
              <Tools>{props.tools}</Tools>
            </Label>
          )}
        </Container>
      )}
    </PermissionedComponent>
  );
};

const StyledInput = styled.div`
  width: 100%;
`;

const Text = styled.div`
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const TipInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const LabelText = styled.div`
  color: ${props => props.theme.colors.foreground};
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  width: 150px;
  margin-right: 1em;
  align-items: center;
`;

const Label = styled.div`
  min-width: 100px;
  line-height: 20px;
  padding-right: 2px;
  text-align: left;
  vertical-align: middle;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 1em;
`;

export const Qtip = styled(Tooltip)`
  padding-left: 1px;
  &:hover {
    cursor: pointer;
  }
`;

export const Tools = styled.div`
  display: flex;
  gap: 4px;
  & > div {
    text-align: right;
  }
  & > div:hover {
    cursor: pointer;
  }
`;

const Container = styled.div<{ isMillerColumn?: boolean; isActive?: boolean }>`
  flex: 1;
  padding: 3px;
  margin-bottom: 8px;

  ${props =>
    props.isActive
      ? css`
          background: ${props => props.theme.colors.brandMuted}66;
        `
      : ''}

  ${Label} {
    ${props =>
      props.isMillerColumn
        ? css`
            min-width 20% !important
            margin-right 10px

            @media (max-width: 1100px)
              text-align left !important
        `
        : ''}
  }
`;

const StyledSelect = styled(Select)`
  .ant-select-selector {
    width: unset !important;
  }
`;

const Locked = styled(Lock)`
  fill: ${props => props.theme.colors.accent};
  font-size: 12px;
`;

const Warning = styled(WarningIcon)`
  fill: ${props => props.theme.colors.accent};
  font-size: 12px;
  margin-right: 6px;
`;
