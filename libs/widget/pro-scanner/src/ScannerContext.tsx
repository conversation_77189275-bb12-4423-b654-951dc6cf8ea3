'use client';
import React from 'react';
import { ScannerFeed, ScannerManager } from '@benzinga/scanner-manager';
import { DEFAULT_SCANNER_CONFIG, ScannerConfig } from '@benzinga/scanner-config-manager';
import { SessionContext } from '@benzinga/session-context';
import { useScannerFilterSource, useScannerFilterableDefs } from '@benzinga/scanner-manager-hooks';
import { useAutoCompleteSymbols } from '@benzinga/pro-ui';
import { DataField, TargetSource } from '@benzinga/quotes-v3-fields-manager';

export const ScannerFeedContext = React.createContext<ScannerFeed | undefined>(undefined);

export const ScannerFeedContextProvider: React.FC<React.PropsWithChildren<{ feed: ScannerFeed }>> = props => {
  return <ScannerFeedContext.Provider value={props.feed}> {props.children} </ScannerFeedContext.Provider>;
};

export const useScannerFeed = (userConfig: ScannerConfig | undefined) => {
  const session = React.useContext(SessionContext);
  const filtersDef = useScannerFilterableDefs();
  const symbols = useAutoCompleteSymbols(userConfig?.tags ?? []);
  const { isFilterMatchSource } = useScannerFilterSource((userConfig?.source as TargetSource) ?? TargetSource.All);

  const mappedConfig = React.useMemo<ScannerConfig>(
    () =>
      userConfig
        ? {
            ...userConfig,
            // add symbols to the filters
            filters: [...userConfig.filters, { field: 'symbol', operator: 'in', parameters: symbols ?? [] }]
              .map(
                filter =>
                  filtersDef?.find(def => def.name === filter.field)?.filterConvert?.(filter, session.getSession()) ??
                  filter,
              )
              .filter(filter => isFilterMatchSource(filtersDef?.find(def => def.name === filter.field) as DataField)),
            tableParameters: {
              ...userConfig.tableParameters,
              columns: userConfig.tableParameters.columns.filter(col => !col.hide),
            },
          }
        : DEFAULT_SCANNER_CONFIG,
    [userConfig, symbols, filtersDef, session, isFilterMatchSource],
  );

  return session.getManager(ScannerManager).getFeed(mappedConfig);
};

export const useScannerFeedContext = () => {
  const session = React.useContext(SessionContext);
  return React.useContext(ScannerFeedContext) ?? session.getManager(ScannerManager).getFeed(DEFAULT_SCANNER_CONFIG);
};
