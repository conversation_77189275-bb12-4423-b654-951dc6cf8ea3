import {
  cellClassRules,
  ChangePercentColDef,
  DateColDef,
  NumberColDef,
  SetColDef,
  SymbolColDef,
  TextColDef,
} from '@benzinga/pro-ui';
import { CellClassRules, ColDef, ColGroupDef } from '@ag-grid-community/core';
import { useTime } from '@benzinga/time-manager-hooks';
import React from 'react';

export const useColumnDef = (): (ColDef | ColGroupDef)[] => {
  const time = useTime();
  const timeInfo = React.useMemo(
    () => ({ timeFormat: time.timeFormat, timeOffset: time.timeOffset }),
    [time.timeFormat, time.timeOffset],
  );
  return React.useMemo<(ColDef | ColGroupDef)[]>(
    () => [
      SymbolColDef({
        field: 'company_ticker',
        headerName: 'Ticker',
        minWidth: 100,
      }),
      TextColDef({
        cellClass: ['u-alignLeft'],
        field: 'company_name',
        headerName: 'Company Name',
        minWidth: 100,
      }),
      SetColDef({
        cellClass: ['u-alignLeft'],
        cellClassRules: cellClassRules.upDownColorState,
        field: 'trade_types',
        headerName: 'Trade Type',
        headerTooltip: 'A breakdown of the type of transaction this filing reported',
      }),
      ChangePercentColDef({
        field: 'owned',
        headerName: 'Δ Own',
        headerTooltip: 'The percentage change in shares held',
        minWidth: 100,
      }),
      NumberColDef({
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        field: 'traded_shares',
        headerName: 'Quantity',
        headerTooltip: 'The total shares acquired/disposed',
        minWidth: 100,
      }),
      NumberColDef({
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        field: 'traded_share_price',
        headerName: 'Price',
        headerTooltip: 'The average price of acquired/disposed shares',
        minWidth: 100,
      }),
      NumberColDef({
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        field: 'traded_value',
        headerName: 'Size ($)',
        headerTooltip: 'The total dollar value of shares traded',
        minWidth: 100,
      }),
      TextColDef({
        cellClass: ['u-alignLeft'],
        field: 'insider_names',
        headerName: 'Insider Names',
        headerTooltip: 'All the names of insiders included in this filing',
        minWidth: 100,
      }),
      SetColDef({
        cellClass: ['u-alignLeft'],
        field: 'insider_titles',
        headerName: 'Title',
        headerTooltip: 'All the titles for insiders included in this filing',
        minWidth: 100,
      }),
      NumberColDef({
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        field: 'owned',
        headerName: 'Owned Shares',
        headerTooltip: 'Remaining shares still owned',
        minWidth: 100,
      }),
      DateColDef(
        {
          cellClass: ['u-alignLeft'],
          field: 'last_filing_date',
          headerName: 'Filing Date',
          headerTooltip: 'The date on which this was filed',
          minWidth: 100,
        },
        timeInfo,
      ),
    ],
    [timeInfo],
  );
};
