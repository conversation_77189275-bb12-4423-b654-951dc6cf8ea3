'use client';
import React from 'react';
import Hooks from '@benzinga/hooks';
import { AdvancedNewsManager, Category, Story, StoryCategory, StoryEvent } from '@benzinga/advanced-news-manager';
import styled, { TC, css } from '@benzinga/themetron';
import {
  TickerColor,
  MultipleTickers,
  SendLinkContext,
  PerformanceColor,
  ProTickerPopover,
  TickerHeaderModule,
  SparkLineModule,
  TickerNotesModule,
  HorizontalRuleModule,
} from '@benzinga/pro-ui';
import { SessionContext } from '@benzinga/session-context';
import { useTime } from '@benzinga/time-manager-hooks';
import { ArticleManager, Author } from '@benzinga/article-manager';

import classnames from 'classnames';

import { isLinkClick } from '../utils/domUtils';
import Tag, { TagsWrapper } from './tag';
import BodyIsLoadingSpinner from './BodyIsLoadingSpinner';
import NewsfeedContent from './content';
import AdvancedNewsfeedStoryTools from './storyTools';
import { NewsfeedStoryBodyWrapper } from './styles';
import { AdvancedNewsfeedStoryContext } from '../context/feedContext';
import { getSymbolName } from '../utils/symbol';
import { StoryIcon } from './storyIcon';
import { ProContext } from '@benzinga/pro-tools';
import { useNewsfeedGlobalSettings } from '../utils/useGlobalSettings';
import { useQuoteSubscription } from '@benzinga/quotes-manager-hooks';
import { Quote, getQuoteChange } from '@benzinga/quotes-manager';
import { DisplayType } from '@benzinga/news-user-settings';
import { Categories } from '@benzinga/themed-icons';
import { parseToHTML } from '@benzinga/frontend-utils';

interface Props {
  allCaps: boolean;
  articleRef: React.RefObject<HTMLDivElement | null>;
  displayType: DisplayType;
  isFresh: boolean;
  narrowWidthLayout: boolean;
  paddingStyle: string;
  renderBody: JSX.Element | null;
  showStoryTools: boolean;
  sourceShortName: string | null;
  story: Story;
  textSize: number;
  widgetWidth: number | undefined;
  computeHeight(): void;
  debugStory(): void;
  popoutStory(): void;
  onLinkClick(event: React.MouseEvent<HTMLElement>): void;
}

type StoryCategoryNode = StoryCategory & { children?: StoryCategoryNode[] };

/**
 * Generate a chain o f Categories from a channel's parent to the channel based on the Category Tree provided
 *
 * @param {channel} Channel to create the chain for
 * @param {storedCategories} storedCategories A tree of all existing categories
 */
const recursiveCategorySearch = (channel: StoryCategory, storedCategories: Category[]): Category[] => {
  return storedCategories.reduce<Category[]>((categoryFound, category) => {
    if (categoryFound.length !== 0) return categoryFound;
    if (category.tid === channel.tid) return [category];
    const result = recursiveCategorySearch(channel, category.children);
    if (result.length !== 0) return [category, ...result];
    return [];
  }, []);
};

/**
 * Traverse a chain of categories that go from parent to child and insert needed nodes into the tree.
 *
 * @param {categoryChain} categoryChain An array whose first element is the top level category parent and last element is the category child for which this chain exists
 * @param {categoryTree} categoryTree The tree to insert the chain into. Each node is a top level category parent, which in turn links to further children
 */
const recursiveCategoryTreeBuild = (categoryChain: Category[], categoryTree: StoryCategoryNode[]): void => {
  if (categoryChain.length === 0) return;
  let relevantTreeNode: StoryCategoryNode | undefined = categoryTree.find(node => node.tid === categoryChain[0].tid);
  if (relevantTreeNode == null) {
    relevantTreeNode = {
      children: [],
      description: categoryChain[0].description,
      name: categoryChain[0].name,
      primary: false,
      tid: categoryChain[0].tid,
      vid: categoryChain[0].vid,
    };
    categoryTree.push(relevantTreeNode);
  } else if (relevantTreeNode.children == null) {
    relevantTreeNode.children = [];
  }
  recursiveCategoryTreeBuild(categoryChain.slice(1), relevantTreeNode.children ?? []);
};

/**
 * Build a tree of channels using given a list of channels to include in the tree. Refer to a tree with all channels that exist.
 *
 * @param {channels} channels An array of channels to create a tree out of
 * @param {storedCategories}  storedCategories A tree of all existing categories
 */
const buildCategoryTree = (channels: StoryCategory[], storedCategories: Category[]): StoryCategoryNode[] => {
  const categoryChains = channels.map<Category[]>(channel => recursiveCategorySearch(channel, storedCategories));
  const categoryTree: StoryCategoryNode[] = [];
  categoryChains.forEach(categoryChain => recursiveCategoryTreeBuild(categoryChain, categoryTree));
  return categoryTree;
};

const AuthorHeader: React.FC<{ story: Story }> = props => {
  const [state, setState] = React.useState<{ author: Author | undefined }>(() => ({ author: undefined }));
  const session = React.useContext(SessionContext);
  React.useEffect(() => {
    fetch(`https://www.benzinga.com/services/content/editorial/preview?key=mz6rEWsR5S&nid=${props.story.getNodeId()}`)
      .then(res => res.json())
      .then(data => {
        return session.getManager(ArticleManager).getAuthor(data.data.uid);
      })
      .then(author => {
        setState(s => ({ ...s, author: author.ok }));
      })
      .catch(() => {
        setState(s => ({ ...s, author: undefined }));
      });
  }, [props.story, session]);

  const authorName = state.author?.name ?? props.story.getAuthor() ?? 'Benzinga Author';
  const authorImg = state.author?.picture
    ? `https://cdn.benzinga.com/${state.author.picture}`
    : 'https://cdn.benzinga.com/sites/all/modules/custom/bz_user/images/no_picture.png';
  return (
    <AuthorHeaderContainer>
      <AuthorInfo authorImg={authorImg} authorName={authorName}></AuthorInfo>
      {state.author?.byLine && <AuthorByline>{state.author?.byLine}</AuthorByline>}
    </AuthorHeaderContainer>
  );
};

const HeaderTicker: React.FC<{ ticker: string; itemWidth: number }> = props => {
  const sendLink = React.useContext(SendLinkContext);
  const { quote } = useQuoteSubscription(props.ticker);
  const { percentageChange } = getQuoteChange<Quote>(quote);
  const isPositive = (percentageChange ?? 0) >= 0;
  const onSymbolClick = React.useCallback<React.MouseEventHandler>(
    e => {
      sendLink.onSymbolClick(props.ticker);
      e.preventDefault();
      e.stopPropagation();
    },
    [props.ticker, sendLink],
  );
  const positionList = React.useMemo(() => ['top' as const, 'bottom' as const], []);
  const modules = React.useMemo(
    () => (
      <>
        <TickerHeaderModule onSymbolClick={sendLink.onSymbolClick} />
        <HorizontalRuleModule />
        <SparkLineModule />
        <HorizontalRuleModule />
        <TickerNotesModule />
      </>
    ),
    [sendLink],
  );
  return (
    <ProTickerPopover modules={modules} positions={positionList} symbol={props.ticker}>
      <HeaderTickerContainer isPositive={isPositive} itemWidth={props.itemWidth} onClickCapture={onSymbolClick}>
        <span style={{ maxWidth: '45%', overflow: 'hidden' }}>
          <PerformanceColor label={props.ticker} stat={percentageChange ?? 0} />
        </span>
        <span style={{ maxWidth: '45%', overflow: 'hidden' }}>
          <PerformanceColor isPercentage={true} stat={percentageChange ?? 0} />
        </span>
      </HeaderTickerContainer>
    </ProTickerPopover>
  );
};

const MoreHeaderTickers: React.FC<{
  itemCount: number;
  moreItemsWidth: number;
  setIsExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  isExpanded: boolean;
}> = props => {
  const onClick = React.useCallback<React.MouseEventHandler>(
    e => {
      const setIsExpanded = props.setIsExpanded;
      setIsExpanded(expanded => !expanded);
      e.stopPropagation();
      e.preventDefault();
    },
    [props.setIsExpanded],
  );
  if (props.itemCount <= 0) {
    return null;
  }
  return (
    <MoreHeaderTickersButton onClickCapture={onClick}>
      {props.isExpanded ? '-' : `${props.itemCount}+`}
    </MoreHeaderTickersButton>
  );
};
const TickerHeader: React.FC<{ story: Story }> = props => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const tickers = props.story.getTickers()?.map(t => getSymbolName(t.name)) ?? [];
  const ref = React.useRef<HTMLDivElement>(null);
  const clientWidth = ref.current?.clientWidth;
  const itemWidth = 120;
  const moreItemsWidth = 30;
  const itemCount = React.useMemo(() => {
    if (clientWidth === undefined) return 0;
    else return Math.min(tickers.length, Math.floor((clientWidth - moreItemsWidth) / itemWidth));
  }, [tickers.length, clientWidth]);

  return (
    <TickerHeaderContainer ref={ref}>
      <HeaderTickers isExpanded={isExpanded}>
        {tickers.slice(0, isExpanded ? tickers.length : itemCount).map(ticker => (
          <HeaderTicker itemWidth={itemWidth} key={ticker} ticker={ticker} />
        ))}
        {tickers.length > itemCount && (
          <MoreHeaderTickers
            isExpanded={isExpanded}
            itemCount={tickers.length - itemCount}
            moreItemsWidth={moreItemsWidth}
            setIsExpanded={setIsExpanded}
          />
        )}
      </HeaderTickers>
    </TickerHeaderContainer>
  );
};

const Header: React.FC<{ story: Story }> = props => {
  return (
    <HeaderContainer>
      <AuthorHeader story={props.story} />
      <TickerHeader story={props.story} />
    </HeaderContainer>
  );
};

export const NewsfeedStoryHeadlines: React.FC<Props> = React.memo(props => {
  const session = React.useContext(SessionContext);
  const {
    closeStoryBody: contextCloseStoryBody,
    isStoryBody: contextIsStoryBody,
    openStoryBody: contextOpenStoryBody,
  } = React.useContext(AdvancedNewsfeedStoryContext);

  const time = useTime();
  const isOpen = contextIsStoryBody(props.story);
  const storedCategories = session.getManager(AdvancedNewsManager).getStoredCategories();
  const proContext = React.useContext(ProContext);
  const newsfeedSettings = useNewsfeedGlobalSettings();

  const update = Hooks.useForceUpdate();

  const toggleStoryBody = React.useCallback(() => {
    if (isOpen === true) {
      contextCloseStoryBody(props.story);
    } else {
      if (props.story.getBody() === undefined) {
        props.story.fetchBody();
      }
      contextOpenStoryBody(props.story);
    }
    update();
  }, [props.story, update, contextCloseStoryBody, contextOpenStoryBody, isOpen]);

  Hooks.useSubscriber(props.story, (_event: StoryEvent) => {
    update();
  });

  const storyBody = props.story.getBody();

  React.useEffect(() => {
    const computeHeight = props.computeHeight;
    computeHeight();
  }, [props.computeHeight, storyBody, isOpen, props.displayType]);

  const onStoryClick = React.useCallback(
    (event: React.MouseEvent<HTMLDivElement | HTMLAnchorElement>) => {
      const story = props.story;
      const onLinkClick = props.onLinkClick;
      const popoutStory = props.popoutStory;
      const hasTeaserText = (story.getTeaserText()?.length ?? 0) > 0;
      const hasCategories = (story.getChannels()?.length ?? 0) > 0;
      const hasContent = hasCategories || hasTeaserText;
      const leftClick = event.button === 0;

      if (!leftClick) {
        return;
      }

      if (!hasContent) {
        popoutStory();
        return;
      }

      if (isLinkClick(event)) {
        onLinkClick(event);
        return;
      }

      const selection = document.getSelection();
      if (selection && !selection.isCollapsed) {
        return;
      }

      toggleStoryBody();
    },
    [props.story, props.onLinkClick, props.popoutStory, toggleStoryBody],
  );

  const getTitleClassName = (): string => {
    const { allCaps } = props;

    return classnames('NewsfeedStory-title', 'NewsfeedStory-title--hasTeaserText', {
      'NewsfeedStory-title--allCaps': allCaps,
    });
  };

  const getPreviewClassName = (shouldAbstractTeaserText: boolean): string => {
    return classnames('NewsfeedStory-preview', {
      'NewsfeedStory-preview--abstractPreview': shouldAbstractTeaserText,
    });
  };

  const sendLink = React.useContext(SendLinkContext);

  const renderTicker = React.useCallback(
    (symbol: string) => {
      return (
        <Ellipsis>
          <TickerColor onSymbolClick={sendLink.onSymbolClick} symbol={symbol} />
        </Ellipsis>
      );
    },
    [sendLink.onSymbolClick],
  );

  const renderMoreTickers = React.useCallback((extraTickers: number) => {
    return <MoreCountSpan> +{extraTickers}</MoreCountSpan>;
  }, []);

  const renderStorySymbols = () => {
    const { isFresh, story } = props;
    const tickers = story.getTickers();
    if (tickers && tickers?.length > 0) {
      const referenceTime = story.getCreatedAtDate()?.valueOf() as unknown as string;
      return (
        <MultipleTickers
          baseQuotes={story.getStoryQuotes()}
          isFresh={isFresh}
          isHoverShowAll
          limit={1}
          onSymbolClick={sendLink.onSymbolClick}
          referenceTime={referenceTime}
          render={renderTicker}
          renderMore={renderMoreTickers}
          symbols={tickers.map(t => ({ ...t, name: getSymbolName(t.name) }))}
        />
      );
    }
    return null;
  };

  const recursiveTagMap = React.useCallback(
    (channels: StoryCategoryNode[], path: string[]): React.ReactNode[][] => {
      return channels.flatMap<React.ReactNode[]>(channel => {
        // if this is undefined, tags will not light up on hover.
        const onClick: React.MouseEventHandler<HTMLDivElement> | undefined = e => {
          e.stopPropagation();
          e.preventDefault();
          proContext.addWidget(
            'advancedNewsfeed',
            {
              parameters: {
                feedSettings: { relevantCategories: { story: [channel.tid.toString()] } },
              },
              widgetVersion: 9,
            },
            'current',
          );
        };
        if (channel.children != null && channel.children.length !== 0) {
          return recursiveTagMap(channel.children, [...path, channel.tid.toString()]).map<React.ReactNode[]>(
            (tags, index) => [
              path.length === 0 ? (
                <Tag breadcrumb={'first'} icon={Categories} key={path.length + '-' + index} onClick={onClick}>
                  {channel.name}
                </Tag>
              ) : (
                <Tag breadcrumb={'middle'} key={path.length + '-' + index} onClick={onClick}>
                  {channel.name}
                </Tag>
              ),
              ...tags,
            ],
          );
        }

        return path.length === 0
          ? [
              [
                <Tag icon={Categories} key={path.length + '-0'} onClick={onClick}>
                  {channel.name}
                </Tag>,
              ],
            ]
          : [
              [
                <Tag breadcrumb={'last'} key={path.length + '-0'} onClick={onClick}>
                  {channel.name}
                </Tag>,
              ],
            ];
      });
    },
    [proContext],
  );

  const Footer: React.FC = React.useCallback(() => {
    const channels = props.story.getChannels();
    let displayChannels: StoryCategoryNode[];

    if ((channels?.length ?? 0) === 0) {
      return null;
    }

    if (!storedCategories) {
      displayChannels = channels?.sort((lhs, rhs) => (lhs.name > rhs.name ? 1 : lhs.name < rhs.name ? -1 : 0)) ?? [];
    } else {
      displayChannels = buildCategoryTree(channels ?? [], storedCategories ?? []);
    }

    return (
      <div className="NewsfeedStory-footer">
        {recursiveTagMap(displayChannels, []).map(tagSet => (
          <TagsWrapper>{tagSet}</TagsWrapper>
        ))}
      </div>
    );
  }, [props.story, recursiveTagMap, storedCategories]);

  const {
    articleRef,
    computeHeight,
    debugStory,
    displayType,
    narrowWidthLayout,
    paddingStyle,
    popoutStory,
    renderBody,
    showStoryTools,
    sourceShortName,
    story,
    textSize,
    widgetWidth,
  } = props;

  const createdAtMoment = story.getCreatedAtOffsetDate();

  const displayTime =
    time.timeFormat === 'AM/PM' ? createdAtMoment.toFormat("hh':'mm':'ssa") : createdAtMoment.toFormat("HH':'mm':'ss");

  // (maximumSize - currentSize) * scalar
  const widthMod = (16 - (textSize || 14)) * 4;
  const timeStyle = { width: `${90 - widthMod}px` };
  const symbolsStyle = { width: `${78 - widthMod}px` };
  const contentWrapperLeftPadding = `${narrowWidthLayout || story.viewWithIframe() ? 12 : 164}px`;

  const hasTeaserText = (story.getTeaserText()?.length ?? 0) > 0;
  const hasShowTeaserText = hasTeaserText && !isOpen;
  const hasCategories = (story.getChannels()?.length ?? 0) > 0;
  const hasContent = hasCategories || hasTeaserText;
  const windowWidth = window.innerWidth;
  const isScreenResponsive =
    !newsfeedSettings.disableResponsiveDisplay && widgetWidth && (widgetWidth < windowWidth * 0.4 || widgetWidth < 400)
      ? true
      : false;
  return (
    <div
      className={classnames(
        'NewsfeedStory',
        { 'NewsfeedStory--hasContent': hasContent },
        { 'NewsfeedStory--isOpen': isOpen },
      )}
      key={story.getStoryId()}
      onClick={onStoryClick}
      ref={articleRef}
      style={{
        fontSize: `${textSize}px`,
        padding: paddingStyle,
        pointerEvents: 'auto',
      }}
    >
      <ArticleWrapper isScreenResponsive={isScreenResponsive}>
        {isScreenResponsive ? (
          <StoryHeader>
            <div className="NewsfeedStory-topRow-details">
              <div className="NewsfeedStory-time" style={timeStyle}>
                {displayTime}
              </div>

              <div className="NewsfeedStory-symbols" style={isScreenResponsive ? undefined : symbolsStyle}>
                {renderStorySymbols()}
              </div>
              <StoryIconDiv>
                <StoryIcon content={story.getTitle() ?? ''} story={story} />
              </StoryIconDiv>
            </div>
            {sourceShortName && <div className="NewsfeedStory-source">{sourceShortName}</div>}
          </StoryHeader>
        ) : (
          <div className="NewsfeedStory-topRow-details">
            <div className="NewsfeedStory-time" style={timeStyle}>
              {displayTime}
            </div>

            <div className="NewsfeedStory-symbols" style={symbolsStyle}>
              {renderStorySymbols()}
            </div>
            <StoryIconDiv>
              <StoryIcon content={story.getTitle() ?? ''} story={story} />
            </StoryIconDiv>
          </div>
        )}
        <div className={getTitleClassName()}>
          <NewsfeedContent
            computeHeight={computeHeight}
            content={story.getTitle() ?? ''}
            story={story}
            storyBody={false}
          />
          {hasShowTeaserText && displayType !== 'Expanded' && (
            <span className={getPreviewClassName(displayType === 'Abstract')}>
              {parseToHTML(story.getTeaserText() ?? '')}
            </span>
          )}
        </div>
        {!isScreenResponsive && sourceShortName && <div className="NewsfeedStory-source">{sourceShortName}</div>}
      </ArticleWrapper>
      {showStoryTools && <AdvancedNewsfeedStoryTools debugStory={debugStory} popoutStory={popoutStory} story={story} />}
      {(isOpen || displayType === 'Expanded') && (
        <NewsfeedStoryBodyWrapper $paddingLeft={contentWrapperLeftPadding} className="NewsfeedStory-content">
          <Header story={props.story} />
          <BodyContainer $isLoaded={story.isBodyLoaded()}>
            <BorderLine />
            {!story.isBodyLoaded() && <BodyIsLoadingSpinner />}
            {renderBody}
          </BodyContainer>
          <Footer />
        </NewsfeedStoryBodyWrapper>
      )}
    </div>
  );
});

const ArticleWrapper = styled.div<{ isScreenResponsive: boolean }>`
  display: flex;
  position: relative;
  flex-wrap: ${props => (props.isScreenResponsive ? 'wrap' : 'nowrap')};
  .NewsfeedStory-title {
    padding-left: ${props => (props.isScreenResponsive ? '6px' : '0px')};
  }
  @media only screen and (max-width: 728px) {
    padding: 0px 5px;
    margin: 0;
    .NewsfeedStory-title {
      padding-left: 1px !important;
    }
  }
`;

const AuthorInfo: React.FC<{ authorName: string; authorImg: string }> = ({ authorImg, authorName }) => {
  return (
    <AuthorContainer>
      <AuthorImg src={authorImg} />
      <AuthorName>{authorName}</AuthorName>
    </AuthorContainer>
  );
};

const AuthorByline = styled.div`
  display: flex;
  height: 20px;
  padding: 2px 6px;
  align-items: center;
  border-radius: 0px 4px 4px 0px;
  border: 1px solid ${props => props.theme.colors.border};

  color: ${props => props.theme.colors.foregroundMuted};
  /* 10/TT */
  font-size: 10px;
  font-family: 'Nimbus Sans D OT';
  font-weight: 700;
  line-height: 14px;
  text-transform: uppercase;
`;

const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding-top: 12px;
  justify-content: space-between;
`;

const AuthorHeaderContainer = styled.div`
  align-items: flex-start;
  display: inline-flex;
  padding-top: 12px;
`;
const HeaderTickers = styled.div<{ isExpanded: boolean }>`
  min-width: 0px;
  max-width: 100%;
`;
const TickerHeaderContainer = styled.div`
  display: inline-flex;
  align-items: flex-start;
  padding-top: 12px;
  font-size: 12px;
  font-family: 'Nimbus Sans D OT';
  font-weight: 700;
  line-height: 16px;
  text-transform: uppercase;
`;
const MoreHeaderTickersButton = styled.div`
  ${props => {
    const marginRight = 2;
    return css`
      align-items: center;
      justify-content: center;
      padding: 3px 0px 1px 0px;
      color: ${props.theme.colorPalette.white};
      flex: 0 0 auto;
      min-width: 20px;
      background: ${props.theme.colors.background};
      text-align: center;
      border-radius: 2px;
      margin-right: ${marginRight}px;
      margin-bottom: 2px;
      height: 20px;
      display: inline-flex;
    `;
  }}
`;
const HeaderTickerContainer = styled.div<{ itemWidth: number; isPositive: boolean }>`
  ${props => {
    const marginRight = 2;
    return css`
      display: inline-flex;
      padding: 3px 0px 1px 0px;
      border-radius: 2px;
      width: ${props.itemWidth - marginRight}px;
      background: ${props.isPositive
        ? `${props.theme.colors.statistic.positive}10`
        : `${props.theme.colors.statistic.negative}10`};
      height: 20px;
      margin-right: ${marginRight}px;
      margin-bottom: 10px;
      flex-shrink: 0;
      justify-content: space-around;
    `;
  }}
`;

const AuthorContainer = styled.div`
  display: flex;
  padding: 0px 8px 0px 0px;
  align-items: center;
  gap: 8px;
  border-radius: 2px 0px 0px 2px;
  overflow: hidden;
  background: ${props => props.theme.colors.background};
`;

const AuthorName = styled.div`
  color: ${props => props.theme.colors.foreground};
  /* 12/Bold */
  font-size: 12px;
  font-family: 'Nimbus Sans D OT';
  font-weight: 700;
  line-height: 16px;
`;

const AuthorImg = styled.img`
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const BodyContainer = styled.div<{ $isLoaded: boolean }>`
  ${props =>
    props.$isLoaded &&
    css`
      display: inline-flex;
      align-items: flex-start;
      gap: 12px;
    `}
  margin-left: -12px;
  width: 100%;
`;

const BorderLine = styled.div`
  width: 2px;
  align-self: stretch;
  border-radius: 0px 4px 4px 0px;
  background: ${props => props.theme.colors.brand};
  min-height: 0px;
`;

const MoreCountSpan = styled.span`
  font-size: 0.75em;
  margin-left: 0.25em;
  vertical-align: top;
`;

const StoryIconDiv = styled.div`
  font-size: 0.8em;
  margin-right: 3px;
  min-width: 1.25em;
  padding: 0.3em;
  width: 1.25em;
`;

const Ellipsis = styled.span`
  display: flex;
  width: fit-content;
`;

const StoryHeader = styled(TC.StretchRow)`
  width: 100%;
`;
