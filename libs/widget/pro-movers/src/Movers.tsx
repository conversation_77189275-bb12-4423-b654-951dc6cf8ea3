'use client';
import { DatePicker, Input, Radio, Select, Slider, message } from 'antd';
import { RadioChangeEvent } from 'antd/lib/radio/interface';
import invariant from 'invariant';
import moment from 'moment-timezone';
import { isEmpty } from '@benzinga/utils';
import styled from '@benzinga/themetron';
import { noop } from '@benzinga/utils';
import { PermissionedComponent, PermissionPrompt, usePermissions, useUser } from '@benzinga/user-context';

import { SelectValue } from 'antd/lib/select';

import MoversGrid from './MoversGrid';
import Spinner from './styles/Spinner';

import {
  ComparingTimePeriod,
  ColumnCustomFilterStyles,
  DatePickerContainer,
  DatesContainerLabel,
  Emphasis,
  extraStylesForRefreshBlock,
  GreenText,
  MoversContainer,
  MoversParameters,
  Parameter,
  ParameterLabel,
  ParametersHeader,
  RedText,
  SliderContainer,
  StyledChevronDownIcon,
  StyledChevronRightIcon,
  StyledSelect,
  StyledSpinner,
  StyledTrendingDownIcon,
  StyledTrendingUpIcon,
  WidgetBody,
  Column,
} from './styles';

import { MarketSession, NBSP, NoResults } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import {
  floatMarks,
  GainersLosersDir,
  intraSessionIntervals,
  marketCapMarks,
  moversSectorOptions,
  moversTimeFormat,
  MoversWidgetParameters,
  outOfSessionIntervals,
  priceMarks,
  volumeMarks,
} from './utils/moversUtils';
import { MECSSectorCode } from '@benzinga/session';
import React from 'react';
import { useWidgetParameters } from '@benzinga/widget-tools';
import { MoversWidgetManifest } from './widget';
import { useMoversData } from './useMoversData';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import calendar from 'dayjs/plugin/calendar';
import { Lock } from '@benzinga/themed-icons';

dayjs.extend(calendar);
dayjs.extend(utc);
dayjs.extend(timezone);

export interface Props {
  className?: string;
  flightMode: boolean;
  setFlightMode: (flightMode: boolean) => void;
  parameters: MoversWidgetParameters;
  sessionContext?: any;
  table: TableParameters;
  loadGrid: (parameters: TableParameters) => void;
  moversUpdateParameters: (parameters: Partial<MoversWidgetParameters>) => void;
}

interface MarketCapOption {
  id: string;
  label: string;
}

interface VolumeOption {
  id: string;
  label: string;
}

interface FloatOption {
  id: string;
  label: string;
}

interface PriceOption {
  id: string;
  label: string;
}

// const DatePicker = AntdDatePicker.generatePicker<Moment>(momentGenerateConfig);
const { Button: RadioButton, Group: RadioGroup } = Radio;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Group: InputGroup } = styled(Input)`
  .ant-input-group-addon {
    background-color: transparent !important;
    border-right: none !important;
  }
`;

const relativeTimeFormat = {
  lastDay: '[Yesterday]',
  lastWeek: 'dddd, MMMM Do YYYY',
  nextDay: '[Tomorrow]',
  nextWeek: 'dddd, MMMM Do YYYY',
  sameDay: '[Today]',
  sameElse: 'dddd, MMMM Do YYYY',
};

const marketCapTuple: ['minimumSliderMarketCap', 'maximumSliderMarketCap'] = [
  'minimumSliderMarketCap',
  'maximumSliderMarketCap',
];

const marketCapCustomTuple: ['minimumCustomMarketCap', 'maximumCustomMarketCap'] = [
  'minimumCustomMarketCap',
  'maximumCustomMarketCap',
];

const volumeCustomTuple: ['minimumCustomVolume', 'maximumCustomVolume'] = [
  'minimumCustomVolume',
  'maximumCustomVolume',
];

const volumeTuple: ['minimumSliderVolume', 'maximumSliderVolume'] = ['minimumSliderVolume', 'maximumSliderVolume'];

const floatCustomTuple: ['minimumCustomFloat', 'maximumCustomFloat'] = ['minimumCustomFloat', 'maximumCustomFloat'];

const floatTuple: ['minimumSliderFloat', 'maximumSliderFloat'] = ['minimumSliderFloat', 'maximumSliderFloat'];

const priceTuple: ['minimumSliderPrice', 'maximumSliderPrice'] = ['minimumSliderPrice', 'maximumSliderPrice'];
const priceCustomTuple: ['minimumCustomPrice', 'maximumCustomPrice'] = ['minimumCustomPrice', 'maximumCustomPrice'];
const DEFAULT_ACTION = 'bzpro/movers/filter';

const numRegex = /\d+(?:,\d+)*(?:\.\d+)?/;
const numRegexWithMultiplier = /\d+(?:,\d+)*(?:\.\d+)?[kKmMbBtT]?/;

export const Movers: React.FC<Props> = props => {
  const widgetParams = useWidgetParameters(MoversWidgetManifest);

  const user = useUser();
  const isFreeSubscription = React.useMemo(
    () =>
      user?.accessType === undefined ||
      user?.accessType?.toLowerCase() === 'none' ||
      user?.accessType?.toLowerCase() === 'free' ||
      user?.accessType?.toLowerCase() === 'anonymous',
    [user?.accessType],
  );
  const hasAccessToAllFilters = usePermissions(
    [{ action: `${DEFAULT_ACTION}/`, resource: '#' }, !isFreeSubscription],
    'or',
  );
  const hasAccessToAllPeriod = usePermissions(
    [{ action: `${DEFAULT_ACTION}/period`, resource: '#' }, hasAccessToAllFilters],
    'or',
  );

  const showMarketCap = usePermissions(
    [{ action: `${DEFAULT_ACTION}/marketcap`, resource: '#' }, hasAccessToAllFilters],
    'or',
  );

  const showPrice = usePermissions([{ action: `${DEFAULT_ACTION}/price`, resource: '#' }, hasAccessToAllFilters], 'or');

  const hasAccessToAllMovers = usePermissions(
    [{ action: `${DEFAULT_ACTION}/movers`, resource: '#' }, hasAccessToAllFilters],
    'or',
  );

  const setParams = React.useCallback(
    (newParams: Partial<MoversWidgetParameters>) => {
      const setParams = widgetParams.setParameters;
      setParams(old => ({ ...old, ...newParams }));
    },
    [widgetParams.setParameters],
  );

  React.useEffect(() => {
    if (!hasAccessToAllPeriod) {
      setParams({ interval: 'session' });
    }
  }, [hasAccessToAllPeriod, setParams]);

  const [state, setState] = React.useState(() => {
    return {
      showUpsellModal: false,
    };
  });

  const onToggleModal = React.useCallback((show: boolean) => {
    setState(old => ({
      ...old,
      showUpsellModal: show,
    }));
  }, []);

  const { data } = useMoversData(props.parameters);

  const renderCollapsedFilterMenu = () => {
    const {
      parameters: { fromDate, gainersLosersDir, interval, session, toDate },
    } = props;

    const emphasize = (input: string | JSX.Element | null) => <Emphasis>{input}</Emphasis>;

    const gainersBlurb = (
      <GreenText>
        <StyledTrendingUpIcon /> Gainers
      </GreenText>
    );
    const losersBlurb = (
      <RedText>
        <StyledTrendingDownIcon /> Losers
      </RedText>
    );

    const MoversType = {
      both: (
        <span>
          {gainersBlurb}
          {' & '}
          {losersBlurb}
        </span>
      ),
      gainers: gainersBlurb,
      losers: losersBlurb,
    }[gainersLosersDir];

    const sessionType = {
      AFTER_MARKET: <span>of the {emphasize(MarketSession.afterHours)} session</span>,
      PRE_MARKET: <span>of the {emphasize(MarketSession.preMarket)}</span>,
      REGULAR: <span>of the {emphasize(MarketSession.regular)} session</span>,
    }[session];

    // TODO it would be great to statically type the keys of this object against MoversInterval once typescript 2.7 drops
    const dateRange = {
      '-15m': <span>over the last {emphasize('15 minutes')}</span>,

      '-180d': <span>over the last {emphasize('6 months')}</span>,

      '-1w': <span>over the last {emphasize('week')}</span>,

      '-30d': <span>over the last {emphasize('month')}</span>,

      '-30m': <span>over the last {emphasize('30 minutes')}</span>,
      /* eslint-disable sort-keys */
      '-5m': <span>over the last {emphasize('5 minutes')}</span>,
      '-60m': <span>over the last {emphasize('hour')}</span>,
      '-90d': <span>over last {emphasize('3 months')}</span>,
      YTD: <span>over the {emphasize('Year to Date')}</span>,
      custom: (
        <span>
          from {emphasize(moment(fromDate || '').format('MM/DD/YYYY'))}
          {NBSP}to {emphasize(moment(toDate || '').format('MM/DD/YYYY'))}
        </span>
      ),
      session: <span>over this {emphasize('session')}</span>,
      /* eslint-enable sort-keys */
    }[interval];

    return (
      <span>
        : {MoversType}
        {' | '}
        {emphasize(getSectorsLabel())}
        {' | '}
        {dateRange}
        {NBSP}
        {sessionType}
        {data === undefined && <StyledSpinner />}
      </span>
    );
  };

  const getSectorsLabel = () => {
    const { sectors } = props.parameters;
    let sectorLabel: string | null = null;
    const matchedSectors = moversSectorOptions.filter(option => sectors.includes(option.id));
    if (!isEmpty(matchedSectors)) {
      sectorLabel = matchedSectors.map(s => s.name).join(', ');
    }
    return sectorLabel;
  };

  // takes a tuple of keys, which are then updated by the rc-slider's onAfterChange callback
  const handleRangeChange =
    <T extends [keyof MoversWidgetParameters, keyof MoversWidgetParameters], K extends number[]>(properties: T) =>
    (values: K) => {
      const { parameters: propsParams } = props;
      const parameters = properties.reduce<Partial<MoversWidgetParameters>>(
        (accumulator: Partial<MoversWidgetParameters>, property: keyof MoversWidgetParameters, index: number) => {
          if (values[0] !== values[1]) {
            accumulator[property as string] = values[index];
            return accumulator;
          } else {
            accumulator[property as string] = propsParams[property] as number;
          }
          return accumulator;
        },
        {},
      );

      setParams(parameters);
    };

  const handleGainersLosersChange = (event: RadioChangeEvent) => {
    const {
      target: { value },
    } = event;
    invariant(
      value === 'gainers' || value === 'losers' || value === 'both',
      'Invalid value for gainers/losers radio group',
    );
    const gainersLosersDir = value as GainersLosersDir;

    setParams({ gainersLosersDir });
  };

  const handleSessionChange = (event: RadioChangeEvent) => {
    const {
      target: { value },
    } = event;
    invariant(
      value === 'PRE_MARKET' || value === 'REGULAR' || value === 'AFTER_MARKET',
      'Invalid value for session type radio group',
    );
    const session = value as 'PRE_MARKET' | 'REGULAR' | 'AFTER_MARKET';

    setParams({ session });
  };

  const handleIntervalChange = (event: RadioChangeEvent) => {
    setParams({ interval: event.target.value });
  };

  const handleAutoRefreshChange = (event: RadioChangeEvent) => {
    const autoRefresh = Boolean(event.target.value);
    setParams({ autoRefresh });
  };

  const handleSectorFiltersChange = (selectValue: SelectValue) => {
    const sectors = selectValue as MECSSectorCode[];
    setParams({ sectors });
  };

  const handlemarketCapFilterChange = (event: RadioChangeEvent) => {
    const {
      parameters: {
        maximumCustomMarketCap = 'Infinity',
        maximumSliderMarketCap = 'Infinity',
        minimumCustomMarketCap = 0,
        minimumSliderMarketCap = 0,
      },
    } = props;

    const filterName = event.target.value;

    const getConvertedFilterValues = (filterName: string) =>
      ({
        Custom: {
          maximumCustomMarketCap: maximumCustomMarketCap,
          minimumCustomMarketCap: minimumCustomMarketCap,
        },

        Slider: {
          maximumSliderMarketCap,
          minimumSliderMarketCap,
        },
      })[filterName];

    const parameters = {
      marketCapFilterValue: filterName,
      ...getConvertedFilterValues(filterName),
    };

    setParams(parameters);
  };

  const handleCustomMarketCapFilterChange =
    (property: string): React.FocusEventHandler<HTMLInputElement> =>
    event => {
      const { parameters: paramsFromProps } = props;
      const parameters = {};

      setState(old => ({
        ...old,
        [property]: event.target.value,
      }));

      const prepareInputValue = (value: string) => {
        const match = value.match(numRegexWithMultiplier)?.[0] ?? '';
        if (match.length !== value.length) {
          message.error({
            content: 'Values should be numbers or valid shorthand',
            key: 'Values should be numbers or valid shorthand',
          });
          setState(old => ({
            ...old,
            [property]: '',
          }));
          return 'Infinity';
        }

        return match.split(',').join('');
      };

      marketCapCustomTuple.forEach((marketCapProperty: string) => {
        parameters[marketCapProperty] =
          property === marketCapProperty
            ? prepareInputValue(event.target.value.toUpperCase())
            : paramsFromProps[marketCapProperty];
      });

      setParams(parameters);
    };

  const handleCustomVolumeFilterChange =
    (property: string): React.FocusEventHandler<HTMLInputElement> =>
    event => {
      const { parameters: paramsFromProps } = props;
      const parameters = {};

      setState(old => ({
        ...old,
        [property]: event.target.value,
      }));

      const prepareInputValue = (value: string) => {
        const match = value.match(numRegexWithMultiplier)?.[0] ?? '';
        if (match.length !== value.length) {
          message.error({
            content: 'Values should be numbers or valid shorthand',
            key: 'Values should be numbers or valid shorthand',
          });
          setState(old => ({
            ...old,
            [property]: '',
          }));
          return 'Infinity';
        }

        return match.split(',').join('');
      };

      volumeCustomTuple.forEach((volumeProperty: string) => {
        parameters[volumeProperty] =
          property === volumeProperty
            ? prepareInputValue(event.target.value.toUpperCase())
            : paramsFromProps[volumeProperty];
      });

      setParams(parameters);
    };

  const handleVolumeFilterChange = (event: RadioChangeEvent) => {
    const {
      parameters: {
        maximumCustomVolume = 'Infinity',
        maximumSliderVolume = 'Infinity',
        minimumCustomVolume = 0,
        minimumSliderVolume = 0,
      },
    } = props;

    const filterName = event.target.value;

    const getConvertedFilterValues = (filterName: string) =>
      ({
        Custom: {
          maximumCustomVolume: maximumCustomVolume,
          minimumCustomVolume: minimumCustomVolume,
        },

        Slider: {
          maximumSliderVolume,
          minimumSliderVolume,
        },
      })[filterName];

    const parameters = {
      volumeFilterValue: filterName,
      ...getConvertedFilterValues(filterName),
    };

    setParams(parameters);
  };

  const handleCustomFloatFilterChange =
    (property: string): React.FocusEventHandler<HTMLInputElement> =>
    event => {
      const { parameters: paramsFromProps } = props;
      const parameters = {};

      setState(old => ({
        ...old,
        [property]: event.target.value,
      }));

      const prepareInputValue = (value: string) => {
        const match = value.match(numRegexWithMultiplier)?.[0] ?? '';
        if (match.length !== value.length) {
          message.error({
            content: 'Values should be numbers or valid shorthand',
            key: 'Values should be numbers or valid shorthand',
          });
          setState(old => ({
            ...old,
            [property]: '',
          }));
          return 'Infinity';
        }

        return match.split(',').join('');
      };

      floatCustomTuple.forEach((floatProperty: string) => {
        parameters[floatProperty] =
          property === floatProperty
            ? prepareInputValue(event.target.value.toUpperCase())
            : paramsFromProps[floatProperty];
      });

      setParams(parameters);
    };

  const handlefloatFilterChange = (event: RadioChangeEvent) => {
    const {
      parameters: {
        maximumCustomFloat = 'Infinity',
        maximumSliderFloat = 'Infinity',
        minimumCustomFloat = 0,
        minimumSliderFloat = 0,
      },
    } = props;

    const filterName = event.target.value;

    const getConvertedFilterValues = (filterName: string) =>
      ({
        Custom: {
          maximumCustomFloat: maximumCustomFloat,
          minimumCustomFloat: minimumCustomFloat,
        },

        Slider: {
          maximumSliderFloat,
          minimumSliderFloat,
        },
      })[filterName];

    const parameters = {
      floatFilterValue: filterName,
      ...getConvertedFilterValues(filterName),
    };

    setParams(parameters);
  };

  const handlePriceFilterChange = (event: RadioChangeEvent) => {
    const {
      parameters: {
        maximumCustomPrice = 'Infinity',
        maximumSliderPrice = 'Infinity',
        minimumCustomPrice = 0,
        minimumSliderPrice = 0,
      },
    } = props;

    const filterName = event.target.value;

    const getConvertedFilterValues = (filterName: string) =>
      ({
        Custom: {
          maximumCustomPrice: maximumCustomPrice,
          minimumCustomPrice: minimumCustomPrice,
        },

        Slider: {
          maximumSliderPrice,
          minimumSliderPrice,
        },
      })[filterName];

    const parameters = {
      priceFilterValue: filterName,
      ...getConvertedFilterValues(filterName),
    };

    setParams(parameters);
  };

  const handleCustomPriceFilterChange =
    (property: string): React.FocusEventHandler<HTMLInputElement> =>
    event => {
      const { parameters: paramsFromProps } = props;
      const parameters = {};

      setState(old => ({
        ...old,
        [property]: event.target.value,
      }));

      const prepareInputValue = (value: string) => {
        const match = value.match(numRegex)?.[0] ?? '';
        if (match.length !== value.length) {
          message.error({
            content: 'Values should be numbers or valid shorthand',
            key: 'Values should be numbers or valid shorthand',
          });
          setState(old => ({
            ...old,
            [property]: '',
          }));
          return 'Infinity';
        }

        return match.split(',').join('');
      };

      priceCustomTuple.forEach((priceProperty: string) => {
        parameters[priceProperty] =
          property === priceProperty
            ? prepareInputValue(event.target.value.toUpperCase())
            : paramsFromProps[priceProperty];
      });

      setParams(parameters);
    };

  const handleLoadGrid = (parameters: TableParameters) => {
    props.loadGrid(parameters);
  };

  const getSelectedInterval = () => {
    const {
      parameters: { interval },
    } = props;
    if (!isFreeSubscription) {
      return interval;
    } else return 'session';
  };

  const renderFullMenu = () => {
    const {
      parameters: {
        autoRefresh,
        floatFilterValue,
        fromDate,
        gainersLosersDir,
        interval,
        marketCapFilterValue,
        maximumCustomFloat = 'Infinity',
        maximumCustomMarketCap = 'Infinity',
        maximumCustomPrice = 'Infinity',
        maximumCustomVolume = 'Infinity',
        maximumSliderFloat = 'Infinity',
        maximumSliderMarketCap = 'Infinity',
        maximumSliderPrice = 'Infinity',
        maximumSliderVolume = 'Infinity',
        minimumCustomFloat = 0,
        minimumCustomMarketCap = 0,
        minimumCustomPrice = 0,
        minimumCustomVolume = 0,
        minimumSliderFloat = 0,
        minimumSliderMarketCap = 0,
        minimumSliderPrice = 0,
        minimumSliderVolume = 0,
        priceFilterValue,
        sectors,
        session,
        toDate,
        volumeFilterValue,
      },
    } = props;

    const fromMoment = fromDate ? dayjs.tz(fromDate, 'America/New_York') : dayjs();
    const toMoment = toDate ? dayjs.tz(toDate, 'America/New_York') : dayjs();

    let debugZone;

    const gainersLosersFilter = (
      <Parameter>
        <ParameterLabel className="TUTORIAL_ScreenerFilters_Movers">Movers</ParameterLabel>
        <RadioGroup
          buttonStyle="solid"
          defaultValue={gainersLosersDir}
          onChange={handleGainersLosersChange}
          size="small"
        >
          <PermissionedComponent
            permissionsOr={[hasAccessToAllMovers, { action: `movers`, resource: 'GainersAndLoosers' }]}
          >
            {access => access && <RadioButton value="both">Gainers &amp; Losers</RadioButton>}
          </PermissionedComponent>
          <PermissionedComponent permissionsOr={[hasAccessToAllMovers, { action: `movers`, resource: 'Gainers' }]}>
            {access => access && <RadioButton value="gainers">Gainers</RadioButton>}
          </PermissionedComponent>
          <PermissionedComponent permissionsOr={[hasAccessToAllMovers, { action: `movers`, resource: 'Loosers' }]}>
            {access => access && <RadioButton value="losers">Losers</RadioButton>}
          </PermissionedComponent>
        </RadioGroup>
      </Parameter>
    );

    const sessionFilters = (
      <PermissionedComponent
        doNotDisplayPrompt={true}
        doNotStopPropagation={true}
        permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/session`, resource: '#' }]}
      >
        {access => (
          <Parameter>
            <ParameterLabel className="TUTORIAL_ScreenerFilters_Session">
              Session
              <PermissionedComponent permission={access}>
                <LockIconWrapper visible={!access} />
              </PermissionedComponent>
            </ParameterLabel>
            <PermissionedComponent permission={access}>
              {access => (
                <RadioGroup
                  buttonStyle="solid"
                  onChange={access ? handleSessionChange : noop}
                  size="small"
                  value={session}
                >
                  <RadioButton value="PRE_MARKET">{MarketSession.preMarket}</RadioButton>
                  <RadioButton value="REGULAR">{MarketSession.regular}</RadioButton>
                  <RadioButton value="AFTER_MARKET">{MarketSession.afterHours}</RadioButton>
                </RadioGroup>
              )}
            </PermissionedComponent>
          </Parameter>
        )}
      </PermissionedComponent>
    );
    const presentDateTime = (theMoment: dayjs.Dayjs, relativeTime?: boolean) => (
      <span>
        {relativeTime ? theMoment.calendar(dayjs(), relativeTimeFormat) : theMoment.format('MMMM Do YYYY')}
        {NBSP}at{NBSP}
        <Emphasis>{theMoment.format('h:mma')}</Emphasis>
      </span>
    );

    const renderPresentDateTime = () => {
      return (
        <DatesContainerLabel>
          comparing {presentDateTime(fromMoment, true)} to {presentDateTime(toMoment, true)}
        </DatesContainerLabel>
      );
    };

    const handleRangePickerChange = (date: any) => {
      const [fromDate, toDate] = date;

      if (fromDate && toDate) {
        setParams({
          fromDate: fromDate.format(moversTimeFormat),
          toDate: toDate.format(moversTimeFormat),
        });
      }
    };

    const renderCustomDateInputs = () => {
      return (
        <DatePickerContainer>
          <RangePicker
            defaultValue={[dayjs(fromMoment), dayjs(toMoment)]}
            disabledDate={current => current.isAfter(dayjs().endOf('day'))}
            format={'MM/DD/YYYY'}
            onChange={handleRangePickerChange}
            size="small"
          />
        </DatePickerContainer>
      );
    };

    const period = {
      AFTER_MARKET: outOfSessionIntervals,
      PRE_MARKET: outOfSessionIntervals,
      REGULAR: intraSessionIntervals,
    }[session];

    const intervalFilter = (
      <PermissionedComponent
        doNotDisplayPrompt={true}
        doNotStopPropagation={true}
        permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/period`, resource: '#' }]}
      >
        {access => (
          <Parameter>
            <ParameterLabel className="TUTORIAL_ScreenerFilters_Period">
              Period
              {
                <PermissionedComponent permission={access}>
                  <LockIconWrapper visible={!access} />
                </PermissionedComponent>
              }
            </ParameterLabel>
            <Column>
              <PermissionedComponent permission={access}>
                {access => (
                  <div>
                    <RadioGroup
                      buttonStyle="solid"
                      onChange={access ? handleIntervalChange : noop}
                      size="small"
                      value={getSelectedInterval()}
                    >
                      {period.map(({ id, label }) => (
                        <RadioButton key={id} value={id}>
                          {label}
                        </RadioButton>
                      ))}
                    </RadioGroup>
                    {interval === 'custom' && renderCustomDateInputs()}
                  </div>
                )}
              </PermissionedComponent>
            </Column>
          </Parameter>
        )}
      </PermissionedComponent>
    );

    const sectorsFilter = (
      <PermissionedComponent
        doNotDisplayPrompt={true}
        doNotStopPropagation={true}
        permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/sector`, resource: '#' }]}
      >
        {access => (
          <Parameter>
            <ParameterLabel className="TUTORIAL_ScreenerFilters_Sectors">
              Sectors
              {
                <PermissionedComponent permission={access}>
                  <LockIconWrapper visible={!access} />
                </PermissionedComponent>
              }
            </ParameterLabel>
            <StyledSelect
              allowClear
              filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
              mode="multiple"
              notFoundContent="Sector Not Found"
              onChange={access ? handleSectorFiltersChange : noop}
              placeholder="All Sectors"
              value={sectors}
            >
              {moversSectorOptions.map(({ id, name }) => (
                <Option key={id} label={name} value={id}>
                  <PermissionedComponent permission={access}>
                    {access => (
                      <div>
                        {
                          <PermissionedComponent permission={access}>
                            <LockIconWrapper visible={!access} />
                          </PermissionedComponent>
                        }
                        {name}
                      </div>
                    )}
                  </PermissionedComponent>
                </Option>
              ))}
            </StyledSelect>
          </Parameter>
        )}
      </PermissionedComponent>
    );

    const setDefaultValue = (inputValue: number | 'Infinity') => {
      const exceptions = ['Infinity', '0', 0];
      if (exceptions.includes(inputValue)) {
        return '';
      }
      return inputValue;
    };

    const renderCustomMarketCapFilter = () => {
      return (
        <InputGroup compact style={{ alignItems: 'center', display: 'flex' }}>
          <Input
            addonBefore="Min"
            defaultValue={setDefaultValue(minimumCustomMarketCap)}
            onBlur={handleCustomMarketCapFilterChange('minimumCustomMarketCap')}
            onChange={e => {
              setState(s => {
                s['minimumCustomMarketCap'] = e.target.value;
                return s;
              });
            }}
            placeholder="0"
            size="small"
            style={{ marginRight: '16px' }}
          />

          <Input
            addonBefore="Max"
            defaultValue={setDefaultValue(maximumCustomMarketCap)}
            onBlur={handleCustomMarketCapFilterChange('maximumCustomMarketCap')}
            onChange={e => {
              setState(s => {
                s['maximumCustomMarketCap'] = e.target.value;
                return s;
              });
            }}
            placeholder="Infinity"
            size="small"
          />
        </InputGroup>
      );
    };

    const renderBaseMarketCapFilter = (granted: boolean) => (
      <SliderContainer>
        <Slider
          dots
          marks={marketCapMarks}
          max={5}
          min={0}
          onChange={granted ? (handleRangeChange(marketCapTuple) as any) : noop}
          range
          step={1}
          style={{ marginBottom: '14px' }}
          tooltip={{ formatter: null }}
          value={[Number(minimumSliderMarketCap), Number(maximumSliderMarketCap)]}
        />
      </SliderContainer>
    );

    const marketCapFilter = () => {
      const isCustomMarketCapFilter = marketCapFilterValue === 'Custom';
      const marketCapOptions: MarketCapOption[] = [
        { id: '1', label: 'Slider' },
        { id: '2', label: 'Custom' },
      ];

      return (
        <PermissionedComponent
          doNotDisplayPrompt={true}
          doNotStopPropagation={true}
          permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/marketcap`, resource: '#' }]}
        >
          {access => (
            <Parameter>
              <ParameterLabel className="TUTORIAL_ScreenerFilters_MarketCap">
                Market Cap ($)
                {
                  <PermissionedComponent permission={access}>
                    <LockIconWrapper visible={!access} />
                  </PermissionedComponent>
                }
              </ParameterLabel>
              <PermissionedComponent permission={access}>
                {access => (
                  <ColumnCustomFilterStyles>
                    <div>
                      {isCustomMarketCapFilter && access
                        ? renderCustomMarketCapFilter()
                        : renderBaseMarketCapFilter(access)}
                    </div>

                    <RadioGroup
                      buttonStyle="solid"
                      onChange={access ? handlemarketCapFilterChange : noop}
                      size="small"
                      value={marketCapFilterValue}
                    >
                      {marketCapOptions.map(({ id, label }) => (
                        <RadioButton key={id} value={label}>
                          {label}
                        </RadioButton>
                      ))}
                    </RadioGroup>
                  </ColumnCustomFilterStyles>
                )}
              </PermissionedComponent>
            </Parameter>
          )}
        </PermissionedComponent>
      );
    };

    const renderCustomPriceFilter = () => {
      return (
        <InputGroup compact style={{ alignItems: 'center', display: 'flex' }}>
          <Input
            addonBefore="Min"
            defaultValue={setDefaultValue(minimumCustomPrice)}
            onBlur={handleCustomPriceFilterChange('minimumCustomPrice')}
            onChange={e => {
              setState(s => {
                s['minimumCustomPrice'] = e.target.value;
                return s;
              });
            }}
            placeholder="0"
            size="small"
            style={{ marginRight: '16px' }}
          />

          <Input
            addonBefore="Max"
            defaultValue={setDefaultValue(maximumCustomPrice)}
            onBlur={handleCustomPriceFilterChange('maximumCustomPrice')}
            onChange={e => {
              setState(s => {
                s['maximumCustomPrice'] = e.target.value;
                return s;
              });
            }}
            placeholder="Infinity"
            size="small"
          />
        </InputGroup>
      );
    };

    const renderBasePriceFilter = (granted: boolean) => (
      <SliderContainer>
        <Slider
          defaultValue={[Number(minimumSliderPrice), Number(maximumSliderPrice)]}
          dots
          marks={priceMarks}
          max={6}
          min={0}
          onChange={granted ? (handleRangeChange(priceTuple) as any) : noop}
          range
          step={1}
          style={{ marginBottom: '14px' }}
          tooltip={{ formatter: null }}
          value={[Number(minimumSliderPrice), Number(maximumSliderPrice)]}
        />
      </SliderContainer>
    );

    const priceFilter = () => {
      const isCustomPriceFilter = priceFilterValue === 'Custom';
      const priceOptions: PriceOption[] = [
        { id: '1', label: 'Slider' },
        { id: '2', label: 'Custom' },
      ];

      return (
        <PermissionedComponent
          doNotDisplayPrompt={true}
          doNotStopPropagation={true}
          permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/price`, resource: '#' }]}
        >
          {access => (
            <Parameter>
              <ParameterLabel className="TUTORIAL_ScreenerFilters_Price">
                Price ($)
                {
                  <PermissionedComponent permission={access}>
                    <LockIconWrapper visible={!access} />
                  </PermissionedComponent>
                }
              </ParameterLabel>
              <PermissionedComponent permission={access}>
                {access => (
                  <ColumnCustomFilterStyles>
                    <div>
                      {isCustomPriceFilter && access ? renderCustomPriceFilter() : renderBasePriceFilter(access)}
                    </div>

                    <RadioGroup
                      buttonStyle="solid"
                      onChange={access ? handlePriceFilterChange : noop}
                      size="small"
                      value={priceFilterValue}
                    >
                      {priceOptions.map(({ id, label }) => (
                        <RadioButton key={id} value={label}>
                          {label}
                        </RadioButton>
                      ))}
                    </RadioGroup>
                  </ColumnCustomFilterStyles>
                )}
              </PermissionedComponent>
            </Parameter>
          )}
        </PermissionedComponent>
      );
    };

    const autoRefreshFilter = (
      <Parameter>
        <ParameterLabel className="TUTORIAL_ScreenerFilters_Refresh">Refresh (1 min)</ParameterLabel>
        <RadioGroup
          buttonStyle="solid"
          onChange={handleAutoRefreshChange}
          size="small"
          style={extraStylesForRefreshBlock}
          value={autoRefresh}
        >
          <RadioButton value>Auto Refresh</RadioButton>
          <RadioButton value={false}>Freeze</RadioButton>

          <ComparingTimePeriod>{fromDate && renderPresentDateTime()}</ComparingTimePeriod>
        </RadioGroup>
      </Parameter>
    );

    const renderCustomVolumeFilter = () => {
      return (
        <InputGroup compact style={{ alignItems: 'center', display: 'flex' }}>
          <Input
            addonBefore="Min"
            defaultValue={setDefaultValue(minimumCustomVolume)}
            onBlur={handleCustomVolumeFilterChange('minimumCustomVolume')}
            onChange={e => {
              setState(s => {
                s['minimumCustomVolume'] = e.target.value;
                return s;
              });
            }}
            placeholder="0"
            size="small"
            style={{ marginRight: '16px' }}
          />

          <Input
            addonBefore="Max"
            defaultValue={setDefaultValue(maximumCustomVolume)}
            onBlur={handleCustomVolumeFilterChange('maximumCustomVolume')}
            onChange={e => {
              setState(s => {
                s['maximumCustomVolume'] = e.target.value;
                return s;
              });
            }}
            placeholder="Infinity"
            size="small"
          />
        </InputGroup>
      );
    };

    const renderBaseVolumeFilter = (granted: boolean) => (
      <SliderContainer>
        <Slider
          defaultValue={[Number(minimumSliderVolume), Number(maximumSliderVolume)]}
          dots
          marks={volumeMarks}
          max={5}
          min={0}
          onChange={granted ? (handleRangeChange(volumeTuple) as any) : noop}
          range
          step={1}
          style={{ marginBottom: '14px' }}
          tooltip={{ formatter: null }}
          value={[Number(minimumSliderVolume), Number(maximumSliderVolume)]}
        />
      </SliderContainer>
    );

    const volumeFilter = () => {
      const isCustomVolumeFilter = volumeFilterValue === 'Custom';
      const volumeOptions: VolumeOption[] = [
        { id: '1', label: 'Slider' },
        { id: '2', label: 'Custom' },
      ];

      return (
        <PermissionedComponent
          doNotDisplayPrompt={true}
          doNotStopPropagation={true}
          permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/volume`, resource: '#' }]}
        >
          {access => (
            <Parameter>
              <ParameterLabel className="TUTORIAL_ScreenerFilters_Volume">
                Volume
                {
                  <PermissionedComponent permission={access}>
                    <LockIconWrapper visible={!access} />
                  </PermissionedComponent>
                }
              </ParameterLabel>
              <PermissionedComponent permission={access}>
                {access => (
                  <ColumnCustomFilterStyles>
                    <div>
                      {isCustomVolumeFilter && access ? renderCustomVolumeFilter() : renderBaseVolumeFilter(access)}
                    </div>

                    <RadioGroup
                      buttonStyle="solid"
                      onChange={access ? handleVolumeFilterChange : noop}
                      size="small"
                      value={volumeFilterValue}
                    >
                      {volumeOptions.map(({ id, label }) => (
                        <RadioButton key={id} value={label}>
                          {label}
                        </RadioButton>
                      ))}
                    </RadioGroup>
                  </ColumnCustomFilterStyles>
                )}
              </PermissionedComponent>
            </Parameter>
          )}
        </PermissionedComponent>
      );
    };

    const renderCustomFloatFilter = () => {
      return (
        <InputGroup compact style={{ alignItems: 'center', display: 'flex' }}>
          <Input
            addonBefore="Min"
            defaultValue={setDefaultValue(minimumCustomFloat)}
            onBlur={handleCustomFloatFilterChange('minimumCustomFloat')}
            onChange={e => {
              setState(s => {
                s['minimumCustomFloat'] = e.target.value;
                return s;
              });
            }}
            placeholder="0"
            size="small"
            style={{ marginRight: '16px' }}
          />

          <Input
            addonBefore="Max"
            defaultValue={setDefaultValue(maximumCustomFloat)}
            onBlur={handleCustomFloatFilterChange('maximumCustomFloat')}
            onChange={e => {
              setState(s => {
                s['maximumCustomFloat'] = e.target.value;
                return s;
              });
            }}
            placeholder="Infinity"
            size="small"
          />
        </InputGroup>
      );
    };

    const renderBaseFloatFilter = (granted: boolean) => (
      <SliderContainer>
        <Slider
          defaultValue={[Number(minimumSliderFloat), Number(maximumSliderFloat)]}
          dots
          marks={floatMarks}
          max={5}
          min={0}
          onChange={granted ? (handleRangeChange(floatTuple) as any) : noop}
          range
          step={1}
          style={{ marginBottom: '14px' }}
          tooltip={{ formatter: null }}
          value={[Number(minimumSliderFloat), Number(maximumSliderFloat)]}
        />
      </SliderContainer>
    );

    const floatFilter = () => {
      const isCustomFloatFilter = floatFilterValue === 'Custom';
      const floatOptions: FloatOption[] = [
        { id: '1', label: 'Slider' },
        { id: '2', label: 'Custom' },
      ];

      return (
        <PermissionedComponent
          doNotDisplayPrompt={true}
          doNotStopPropagation={true}
          permissionsOr={[hasAccessToAllFilters, { action: `${DEFAULT_ACTION}/float`, resource: '#' }]}
        >
          {access => (
            <Parameter>
              <ParameterLabel className="TUTORIAL_ScreenerFilters_Float">
                Float
                {
                  <PermissionedComponent permission={access}>
                    <LockIconWrapper visible={!access} />
                  </PermissionedComponent>
                }
              </ParameterLabel>
              <PermissionedComponent permission={access}>
                {access => (
                  <ColumnCustomFilterStyles>
                    <div>
                      {isCustomFloatFilter && access ? renderCustomFloatFilter() : renderBaseFloatFilter(access)}
                    </div>

                    <RadioGroup
                      buttonStyle="solid"
                      onChange={access ? handlefloatFilterChange : noop}
                      size="small"
                      value={floatFilterValue}
                    >
                      {floatOptions.map(({ id, label }) => (
                        <RadioButton key={id} value={label}>
                          {label}
                        </RadioButton>
                      ))}
                    </RadioGroup>
                  </ColumnCustomFilterStyles>
                )}
              </PermissionedComponent>
            </Parameter>
          )}
        </PermissionedComponent>
      );
    };

    return (
      <Column>
        {gainersLosersFilter}
        {sessionFilters}
        {intervalFilter}
        {sectorsFilter}
        {marketCapFilter()}
        {volumeFilter()}
        {floatFilter()}
        {priceFilter()}
        {autoRefreshFilter}
        {debugZone}
      </Column>
    );
  };

  const renderContent = () => {
    const { className, table } = props;

    // on first load, instruments will be null in the absence of an error.
    if (data) {
      if (data.length === 0) return <NoResults />;
      return (
        <MoversGrid
          className={className}
          loadGrid={handleLoadGrid}
          rowData={data}
          showMarketCap={showMarketCap}
          showPrice={showPrice}
          table={table}
        />
      );
    } else {
      return <Spinner />;
    }
  };

  const onFilterExpanded = React.useCallback(() => {
    const set = widgetParams.setParameters;
    set(old => ({ ...old, filtersExpanded: !old.filtersExpanded }));
  }, [widgetParams.setParameters]);

  const {
    parameters: { filtersExpanded, flightMode },
  } = props;
  const header = (
    <ParametersHeader $expanded={filtersExpanded} className="TUTORIAL_Screener-FilterMenu" onClick={onFilterExpanded}>
      {filtersExpanded ? <StyledChevronDownIcon /> : <StyledChevronRightIcon />}
      Applied Filters{!filtersExpanded && renderCollapsedFilterMenu()}
    </ParametersHeader>
  );

  return (
    <MoversContainer>
      <MoversParameters $expanded={!flightMode && filtersExpanded}>
        {!flightMode && header}
        {filtersExpanded && !flightMode && renderFullMenu()}
      </MoversParameters>
      <WidgetBody>{renderContent()}</WidgetBody>
      {state.showUpsellModal && <PermissionPrompt onClose={onToggleModal} />}
    </MoversContainer>
  );
};

const LockIconWrapper = styled(Lock)<{ visible: boolean }>`
  visibility: ${props => (props.visible ? 'visible' : 'hidden')};
  margin-left: 5px;
  margin-right: 5px;
  fill: ${props => props.theme.colors.accent} !important;
  height: 0.8em;
  width: 0.8em;
  &:hover {
    cursor: pointer;
  }
`;
