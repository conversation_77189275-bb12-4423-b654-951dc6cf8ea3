'use client';
import React, { useEffect, useMemo } from 'react';
import { StockSymbol } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { addTutorialClasses } from '@benzinga/frontend-utils';
import { ColDef, GridApi, GridOptions } from '@ag-grid-community/core';
import {
  BenzingaGrid,
  ChangeColDef,
  ChangePercentColDef,
  GridExportParams,
  ShortenedNumberColDef,
  PriceColDef,
  SendLinkContext,
  SetColDef,
  SymbolColDef,
  TextColDef,
  PositiveNegativeIndicator,
  NoResults,
} from '@benzinga/pro-ui';
import { LoggingManager } from '@benzinga/session';
import { gridOptions } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { usePeers } from '@benzinga/securities-manager-hooks';
import { useScannerInstruments } from '@benzinga/scanner-manager-hooks';

interface Props {
  symbol: StockSymbol;
  loadGrid: (parameters: Partial<TableParameters>) => void;
  table: TableParameters;
}

const scannerFilters = [{ field: 'type', operator: 'in', parameters: ['STOCK'] }];
const scannerConfig = { refreshInterval: 10 };

const SymbolDetailsPeers: React.FC<Props> = props => {
  const session = React.useContext(SessionContext);
  const peersColumnDefs: ColDef[] = React.useMemo(() => {
    return [
      SymbolColDef({
        field: 'symbol',
        headerName: 'Symbol',
        minWidth: 100,
      }),
      TextColDef({
        field: 'name',
        headerName: 'Name',
        minWidth: 100,
      }),
      SetColDef({
        field: 'exchange',
        headerName: 'Exchange',
      }),
      PriceColDef({
        field: 'price',
        headerName: 'Price',
        minWidth: 100,
      }),
      ChangeColDef({
        field: 'change',
        headerName: 'Change ($)',
      }),
      ChangePercentColDef({
        field: 'changePercent',
        headerName: 'Change (%)',
      }),
      ShortenedNumberColDef({
        field: 'marketCap',
        headerName: 'Market Cap',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'sharesOutstanding',
        headerName: 'Shares Outstanding',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'shareFloat',
        headerName: 'Float',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'priceToEarnings',
        headerName: 'P/E (TTM)',
        minWidth: 100,
      }),
      // ShortenedNumberColDef({
      //   field: 'forwardPERatio',
      //   headerName: 'P/E (Forward)',
      //   minWidth: 100,
      // }),
      ShortenedNumberColDef({
        field: 'dividend',
        headerName: 'Dividend',
        minWidth: 100,
      }),
      ChangePercentColDef({
        field: 'dividendYield',
        headerName: 'Dividend Yield',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'momentumPercentile',
        headerName: 'Momentum Percentile',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'growthPercentile',
        headerName: 'Growth Percentile',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'valuePercentile',
        headerName: 'Value Percentile',
        minWidth: 100,
      }),
      ShortenedNumberColDef({
        field: 'qualityPercentile',
        headerName: 'Quality Percentile',
        minWidth: 100,
      }),
      {
        cellRenderer: params =>
          params.value ? (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <PositiveNegativeIndicator small value={params.value === 'Y'} />
            </div>
          ) : (
            '-'
          ),
        field: 'shortTermTrend',
        headerName: 'Short',
        minWidth: 80,
      },
      {
        cellRenderer: params =>
          params.value ? (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <PositiveNegativeIndicator small value={params.value === 'Y'} />
            </div>
          ) : (
            '-'
          ),
        field: 'mediumTermTrend',
        headerName: 'Medium',
        minWidth: 100,
      },
      {
        cellRenderer: params =>
          params.value ? (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <PositiveNegativeIndicator small value={params.value === 'Y'} />
            </div>
          ) : (
            '-'
          ),
        field: 'longTermTrend',
        headerName: 'Long',
        minWidth: 80,
      },
    ];
  }, []);

  const peersFields = useMemo(() => {
    return ['symbol'];
  }, []);
  const peers = usePeers(props.symbol, peersFields, true);

  const targetSymbols = useMemo(() => {
    if (peers === undefined) {
      return [];
    }

    const peersSymbols = peers?.map(quote => quote.symbol) ?? [];
    return [props.symbol, ...peersSymbols];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [peers]);

  const scannerColumns = useMemo(
    () => [...peersColumnDefs.map(field => field?.field ?? ''), 'type'],
    [peersColumnDefs],
  );

  const { scannerData } = useScannerInstruments(targetSymbols, scannerColumns, scannerFilters, scannerConfig);

  const pinnedData = useMemo(() => {
    if (scannerData.length) {
      const indexOfPrimary = scannerData.findIndex(item => item.symbol === props.symbol);

      if (indexOfPrimary >= 0) {
        return scannerData.splice(indexOfPrimary, 1);
      }
    }
    return null;
  }, [scannerData, props.symbol]);

  const gridApi = React.useRef<GridApi | null | undefined>(null);
  const [exportParams, setExportParams] = React.useState<GridExportParams>({ filename: `${props.symbol} Peers` });

  const localColumnDefs = React.useMemo(() => addTutorialClasses(peersColumnDefs, 'Peers'), [peersColumnDefs]);

  const localGridOptions = React.useMemo<GridOptions>(
    () => ({
      enableRangeSelection: true,
      getRowId: params => params.data.symbol,
      headerHeight: 30,
      immutableData: true,
      onGridReady: params => {
        if (params) {
          gridApi.current = params.api;
        }
      },
      rowHeight: 25,
      rowSelection: 'multiple',
      sideBar: gridOptions.sideBar,
      statusBar: gridOptions.statusBar,
    }),
    [],
  );

  const defaultColDef = React.useMemo<ColDef>(
    () => ({
      cellClass: 'u-alignRight',
      filter: 'number',
      headerClass: ['u-alignLeft'],
      minWidth: 100,
      onCellClicked: cell => {
        session.getManager(LoggingManager).log(
          'debug',
          {
            category: 'details peers',
            message: `${cell}`,
          },
          ['console'],
        );
      },
      resizable: true,
      sortable: true,
    }),
    [session],
  );

  useEffect(() => {
    setTimeout(() => {
      gridApi.current?.setGridOption('pinnedTopRowData', pinnedData?.length ? pinnedData : []);
    });
  }, [pinnedData]);

  React.useEffect(() => {
    if (`${props.symbol} Peers` !== exportParams.filename) {
      setExportParams({ filename: `${props.symbol} Peers` });
    }
  }, [props.symbol, exportParams.filename]);

  const deselectAll = () => {
    if (!gridApi.current) {
      return;
    }
    gridApi.current.deselectAll();
    gridApi.current.clearRangeSelection();
  };

  const handleClickOff = React.useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if ((event.target as HTMLDivElement)?.className?.includes('ag-body-viewport')) {
      deselectAll();
    }
  }, []);

  const updateGridParameters = React.useCallback(
    (parameters: Partial<TableParameters>) => {
      const loadGrid = props.loadGrid;
      loadGrid(parameters);
    },
    [props.loadGrid],
  );

  const { table } = props;

  const sendLink = React.useContext(SendLinkContext);

  if (peers === undefined || peers.length === 0) {
    return <NoResults />;
  }

  return (
    <div className="ag-container" style={{ height: '100%' }}>
      <div className="ag-theme-benzinga" onClick={handleClickOff} style={{ height: '100%' }}>
        <BenzingaGrid
          columnDefs={localColumnDefs}
          defaultColDef={defaultColDef}
          exportParams={exportParams}
          gridLayout={table}
          gridOptions={localGridOptions}
          onGridLayoutChanged={updateGridParameters}
          onSymbolClick={sendLink.onSymbolClick}
          rowClass="TUTORIAL_ag_header_Peers"
          rowData={scannerData}
          sizeColumnsToFit={true}
          suppressRowTransform
          symbolColIDs={['symbol']}
        />
      </div>
    </div>
  );
};

export default React.memo(SymbolDetailsPeers);
