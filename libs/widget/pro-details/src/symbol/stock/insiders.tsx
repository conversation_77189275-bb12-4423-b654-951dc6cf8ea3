'use client';
/* eslint-disable @typescript-eslint/member-ordering */
import React from 'react';
import { addTutorialClasses } from '@benzinga/frontend-utils';
import { ColDef, GridApi, GridOptions, RowDoubleClickedEvent } from '@ag-grid-community/core';
import {
  BenzingaGrid,
  ChangeColDef,
  ChangeDollarColDef,
  ChangePercentColDef,
  ChangeShortenedNumberColDef,
  gridOptions,
  SendLinkContext,
  SetColDef,
  TextColDef,
  DateColDef,
  NoResults,
} from '@benzinga/pro-ui';
import { DateTime } from 'luxon';
import { getTimeDisplayFormat } from '@benzinga/date-utils';
import { PermissionOverlay } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { SessionContext } from '@benzinga/session-context';
import { LoggingManager } from '@benzinga/session';
import { useTime } from '@benzinga/time-manager-hooks';
import { SecuritiesManager } from '@benzinga/securities-manager';
import { StockSymbol } from '@benzinga/session';
import { InsidersFiling } from '@benzinga/insider-trades-manager';

type Props = {
  loadGrid(parameters: Partial<TableParameters>): void;
  table: TableParameters;
  symbol: StockSymbol;
};

const SymbolDetailsInsiders = (props: Props) => {
  const { loadGrid } = props;
  const session = React.useContext(SessionContext);
  const time = useTime();

  const [insiders, setInsiders] = React.useState<InsidersFiling[] | undefined>(undefined);

  React.useEffect(() => {
    const getInsiders = async () => {
      setInsiders(undefined);
      const peers = await session.getManager(SecuritiesManager).getInsiders(props.symbol);
      if (peers.err) {
        setInsiders([]);
      } else {
        setInsiders(peers.ok ?? []);
      }
    };
    getInsiders();
  }, [props.symbol, session]);

  const myGridOptions: GridOptions = React.useMemo(
    () => ({
      defaultColDef: {
        cellClass: 'u-alignRight',
        filter: 'number',
        filterParams: {},
        headerClass: ['u-alignLeft'],
        minWidth: 100,
        onCellClicked: cell => {
          session.getManager(LoggingManager).log(
            'debug',
            {
              category: 'details insider',
              message: `${cell}`,
            },
            ['console'],
          );
        },
        resizable: true,
        sortable: true,
      },
      enableRangeSelection: true,
      getRowId: props => props.data.company_ticker + props.data.last_filing_date,
      headerHeight: 30,
      immutableData: true,
      onGridReady: params => {
        if (params) {
          gridApi.current = params.api;
        }
      },
      processCellForClipboard: params => {
        if (params.column.getColId() === 'last_filing_date') {
          const format = getTimeDisplayFormat({ timeFormat: time.timeFormat });
          const icoTime = DateTime.fromISO(params.value)
            .plus({ minutes: time.timeOffset })
            .toFormat(`yyyy-MM-dd ${format}`);
          return icoTime !== 'Invalid DateTime' ? icoTime : params.value;
        }
        return params.value;
      },
      rowHeight: 20,
      rowSelection: 'multiple' as GridOptions['rowSelection'],
      sideBar: gridOptions.sideBar,
      statusBar: gridOptions.statusBar,
    }),
    [time.timeFormat, time.timeOffset, session],
  );

  const InsidersColumnDefs = React.useMemo<ColDef[]>(() => {
    return [
      DateColDef(
        {
          field: 'last_filing_date',
          headerName: 'Filing Date',
          headerTooltip: 'The date on which this was filed',
          minWidth: 100,
        },
        { timeFormat: time.timeFormat, timeOffset: time.timeOffset },
      ),
      TextColDef({
        field: 'company_name',
        headerName: 'Company Name',
        minWidth: 100,
      }),
      SetColDef({
        field: 'trade_types',
        headerName: 'Trade Type',
        headerTooltip: 'A breakdown of the type of transaction this filing reported',
      }),
      ChangePercentColDef({
        field: 'traded_percentage_string',
        headerName: 'Δ Own',
        headerTooltip: 'The percentage change in shares held',
        minWidth: 100,
      }),
      ChangeColDef({
        field: 'traded_shares',
        headerName: 'Quantity',
        headerTooltip: 'The total shares acquired/disposed',
        minWidth: 100,
      }),
      ChangeDollarColDef({
        field: 'traded_share_price',
        headerName: 'Price',
        headerTooltip: 'The average price of acquired/disposed shares',
        minWidth: 100,
      }),
      ChangeShortenedNumberColDef({
        field: 'traded_value',
        headerName: 'Size ($)',
        headerTooltip: 'The total dollar value of shares traded',
        minWidth: 100,
      }),
      TextColDef({
        field: 'insider_names',
        headerName: 'Insider Names',
        headerTooltip: 'All the names of insiders included in this filing',
        minWidth: 100,
      }),
      SetColDef({
        field: 'insider_titles',
        headerName: 'Title',
        headerTooltip: 'All the titles for insiders included in this filing',
        minWidth: 100,
      }),
      ChangeShortenedNumberColDef({
        field: 'owned',
        headerName: 'Owned Shares',
        headerTooltip: 'Remaining shares still owned',
        minWidth: 100,
      }),
    ];
  }, [time.timeFormat, time.timeOffset]);

  const columnDefs: ColDef[] = React.useMemo(
    () => addTutorialClasses(InsidersColumnDefs, 'Insiders'),
    [InsidersColumnDefs],
  );
  const gridApi = React.useRef<GridApi | null>(null);
  const defaultColDef = React.useMemo<ColDef>(
    () => ({
      cellClass: 'u-alignRight',
      headerClass: ['u-alignLeft'],
      minWidth: 100,
      onCellClicked: cell => {
        session.getManager(LoggingManager).log(
          'debug',
          {
            category: 'details insider',
            message: `${cell}`,
          },
          ['console'],
        );
      },
      resizable: true,
      sortable: true,
    }),
    [session],
  );

  const [exportParams, setExportParams] = React.useState({
    filename: `${props.symbol} Insiders`,
  });

  const [refreshGrid, setRefreshGrid] = React.useState(0);

  React.useEffect(() => {
    if (time.timeFormat) {
      setRefreshGrid(old => old + 1);
    }
  }, [time.timeFormat]);

  React.useEffect(() => {
    setExportParams({
      filename: `${props.symbol} Insiders`,
    });
  }, [props.symbol]);

  const deselectAll = React.useCallback(() => {
    if (!gridApi.current) {
      return;
    }
    gridApi.current.deselectAll();
    gridApi.current.clearRangeSelection();
  }, []);

  const handleClickOff = React.useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (event.target && (event.target as HTMLDivElement).className?.includes('ag-body-viewport')) {
        deselectAll();
      }
    },
    [deselectAll],
  );

  const onRowDoubleClicked = React.useCallback((event: RowDoubleClickedEvent) => {
    const filing = event.data as InsidersFiling;
    window.open(filing.url);
  }, []);

  const updateGridParameters = React.useCallback(
    (parameters: Partial<TableParameters>) => {
      loadGrid(parameters);
    },
    [loadGrid],
  );

  const sendLink = React.useContext(SendLinkContext);

  if (insiders === undefined || insiders.length === 0) {
    return <NoResults />;
  }

  return (
    <PermissionOverlay
      permission={{ action: 'bzpro/widget/use', resource: 'insider-trades' }}
      text={{
        denied_logged_in: `It looks like you don't have permission to view Insiders. You will need a subscription to access this feature.`,
        denied_logged_out: `It looks like you don't have permission to view Insiders.  Login to view Insiders`,
      }}
    >
      <div className="ag-container" style={{ height: '100%' }}>
        <div className="ag-theme-benzinga" onClick={handleClickOff} style={{ height: '100%' }}>
          <BenzingaGrid
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            exportParams={exportParams}
            gridLayout={props.table}
            gridOptions={myGridOptions}
            onGridLayoutChanged={updateGridParameters}
            onRowDoubleClicked={onRowDoubleClicked}
            onSymbolClick={sendLink.onSymbolClick}
            refreshGrid={refreshGrid}
            rowClass="TUTORIAL_ag_header_Insiders"
            rowData={insiders}
            sizeColumnsToFit={true}
            suppressRowTransform
            symbolColIDs={['symbol']}
            tooltipShowDelay={0}
          />
        </div>
      </div>
    </PermissionOverlay>
  );
};

export default SymbolDetailsInsiders;
