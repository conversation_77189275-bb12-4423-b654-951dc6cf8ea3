import { SubscriberContainer, UniqueArrayBuffer } from '@benzinga/containers';
import Hooks from '@benzinga/hooks';
import { GridTransaction, useViewportData } from '@benzinga/pro-ui';
import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import { useWatchlistHoldingsQuotesCol } from '@benzinga/watchlist-manager-hooks';
import React from 'react';
import { Note } from '@benzinga/notes-manager';
import { StockSymbol } from '@benzinga/session';
import { Watchlist } from '@benzinga/watchlist-manager';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { GridApi } from '@ag-grid-community/core';
import { arrayDeepIntersection } from '@benzinga/utils';
import { DEFAULT_SCANNER_CONFIG } from '@benzinga/scanner-config-manager';
import { SessionContext } from '@benzinga/session-context';
import { ScannerManager } from '@benzinga/scanner-manager';
interface DataProps {
  watchlist: Watchlist;
  gridLayout: TableParameters;
  gridApi: React.MutableRefObject<GridApi<any> | null>;
}

type WatchlistGridHolder = HoldingsQuote & { notes?: { notes: Note[]; symbol: StockSymbol } };

export const useDataHook = (props: DataProps) => {
  const session = React.useContext(SessionContext);
  const { getVisibleData, initOnScrollEnd } = useViewportData();

  const initialVisibleData = React.useMemo(() => {
    const { fields, symbols } = getVisibleData(props.gridApi.current);
    return { fields, symbols };
  }, [getVisibleData, props.gridApi]);

  const [visibleData, setVisibleData] = React.useState(initialVisibleData);

  const [hasInitialQuotesLoaded, setHasInitialQuotesLoaded] = React.useState(false);
  const [collection] = React.useState(
    () =>
      new SubscriberContainer<WatchlistGridHolder, UniqueArrayBuffer<WatchlistGridHolder>>(
        Infinity,
        new UniqueArrayBuffer<WatchlistGridHolder>((item: WatchlistGridHolder) => item.symbol),
      ),
  );

  const updateTransaction = React.useCallback(
    (quote: HoldingsQuote) => {
      const existingData = collection.getBufferedItems().find(item => item.symbol === quote.symbol);

      if (existingData || !hasInitialQuotesLoaded) {
        const updatedQuote = {
          ...existingData,
          ...quote,
        };
        collection.updateItems([updatedQuote]);
      }
    },
    [collection, hasInitialQuotesLoaded],
  );

  const updateMultipleTransaction = React.useCallback(
    (quotes: HoldingsQuote[]) => {
      if (!hasInitialQuotesLoaded) {
        const existingItems = collection.getBufferedItems();
        const newItems = [...existingItems];

        quotes.forEach(quote => {
          const existingIndex = newItems.findIndex(item => item.symbol === quote.symbol);
          if (existingIndex >= 0) {
            newItems[existingIndex] = { ...newItems[existingIndex], ...quote };
          } else {
            newItems.push(quote);
          }
        });

        collection.replace(newItems);

        setHasInitialQuotesLoaded(true);
        return;
      }

      const updatedQuotes = quotes.map(quote => {
        const existingData = collection.getBufferedItems().find(item => item.symbol === quote.symbol);
        return {
          ...existingData,
          ...quote,
        };
      });

      collection.updateItems(updatedQuotes);
    },
    [collection, hasInitialQuotesLoaded],
  );

  const [transaction] = React.useState(() => new GridTransaction<HoldingsQuote>());
  Hooks.useSubscriber(collection, event => {
    switch (event.type) {
      case 'update':
        if (props.watchlist.symbols.length === 0) {
          transaction.flush();
          collection.clear();
          props.gridApi.current?.setRowData([]);
          return;
        }
        transaction.transaction(event);
        break;
    }
  });

  const updateTransactionThrottled = Hooks.useThrottleLeading(updateTransaction, 30);
  const updateMultipleTransactionThrottled = Hooks.useThrottleLeading(updateMultipleTransaction, 30);

  const requiredFields = React.useMemo(
    () =>
      new Set<keyof HoldingsQuote>(
        arrayDeepIntersection(visibleData.fields, visibleData.fields) as Array<keyof HoldingsQuote>,
      ),
    [visibleData.fields],
  );

  const filteredSymbols = React.useMemo(
    () => arrayDeepIntersection(visibleData.symbols, visibleData.symbols),
    [visibleData.symbols],
  );

  useWatchlistHoldingsQuotesCol(
    props.watchlist.watchlistId,
    requiredFields,
    updateTransactionThrottled,
    filteredSymbols,
  );

  const limit = 700;

  const refreshInterval = React.useMemo(
    () => ((props.watchlist.symbols.length * 1000) / limit < 1 ? 1 : (props.watchlist.symbols.length * 1000) / limit),
    [props.watchlist.symbols.length],
  );

  const fetchQuotes = React.useCallback(
    async (startIndex, endIndex) => {
      const quotes = await session.getManager(ScannerManager).getInstruments({
        ...DEFAULT_SCANNER_CONFIG,
        filters: [
          {
            field: 'symbol',
            operator: 'in',
            parameters: props.watchlist.symbols.slice(startIndex, endIndex).map(w => w.symbol) ?? [],
          },
        ],
        limit,
        tableParameters: {
          ...DEFAULT_SCANNER_CONFIG.tableParameters,
          columns: props.gridLayout.columns.map(c => ({ colId: c.colId })),
        },
      });

      if (quotes.err) {
        return;
      }

      return quotes.ok.instruments;
    },
    [props.gridLayout.columns, props.watchlist.symbols, session],
  );

  const InitialQuoteFetch = React.useCallback(async () => {
    if (hasInitialQuotesLoaded) return;

    for (let i = 0; i <= props.watchlist.symbols.length; i += limit) {
      const quotes = await fetchQuotes(i, i + limit);
      updateMultipleTransactionThrottled(quotes as HoldingsQuote[]);
    }

    setTimeout(() => {
      setHasInitialQuotesLoaded(true);
      const { fields, symbols } = getVisibleData(props.gridApi.current);
      setVisibleData({ fields, symbols });
    }, refreshInterval);
  }, [
    fetchQuotes,
    getVisibleData,
    hasInitialQuotesLoaded,
    props.gridApi,
    props.watchlist.symbols.length,
    refreshInterval,
    updateMultipleTransactionThrottled,
  ]);

  React.useEffect(() => {
    collection.replace([]);
    setHasInitialQuotesLoaded(false);
  }, [collection, props.watchlist.watchlistId]);

  React.useEffect(() => {
    InitialQuoteFetch();
  }, [props.watchlist.watchlistId, InitialQuoteFetch]);

  React.useEffect(() => {
    initOnScrollEnd(props.gridApi.current, visibleData => {
      setVisibleData(visibleData);
    });

    return () => {
      if (props.gridApi.current) {
        props.gridApi.current.removeEventListener('bodyScrollEnd', initOnScrollEnd);
      }
    };
  }, [initOnScrollEnd, props.gridApi]);

  return { hasInitialQuotesLoaded, setHasInitialQuotesLoaded, transaction };
};
