{"affectedProjects": ["amp", "bz", "bz-e2e", "bz-mobile", "data-manager-advanced-news", "data-manager-alts", "data-manager-article", "data-manager-basic-news", "data-manager-content", "data-manager-internal-news", "data-manager-legacy-scanner", "data-manager-news-alerts", "data-manager-quotes-v3", "data-manager-quotes-v3-holdings", "data-manager-scanner", "data-manager-scanner-config", "data-manager-watchlists-holdings", "data-managers-examples", "data-managers-quotes-v3-fields", "icons-v<PERSON><PERSON><PERSON>", "india", "legacy-fission", "money", "news-user-settings", "newsdesk-tools", "pro", "pro-e2e", "proto", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-news-manager", "react-utils-data-hooks-quotes-manager", "react-utils-data-hooks-quotes-v3-manager", "react-utils-data-hooks-scanner-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-pro-tools", "react-utils-user-context", "react-utils-widget-tools", "ui-ads", "ui-ads-utils", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calculators", "ui-calendars", "ui-charts", "ui-comments", "ui-core", "ui-crypto", "ui-edge", "ui-entity", "ui-filter", "ui-forms", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-search", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-user", "ui-watchlist-ui", "ui-widgets", "utils-analytics", "utils-blocks", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-charting-libarary", "visualization-trading-view-light-weight-chart", "visualization-visualization-utils", "widget-chartwidget", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-option-chain", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder", "widgets-pro-calendar", "widgets-pro-insiders", "wnstn-chat"], "description": "allow the crypto data source in the scanner to gracefully work with the equity data source", "epic": null, "issueNumber": "12971", "project": "PRO", "projects": ["amp", "bz-e2e", "bz-mobile", "data-manager-advanced-news", "data-manager-alts", "data-manager-article", "data-manager-basic-news", "data-manager-content", "data-manager-internal-news", "data-manager-legacy-scanner", "data-manager-news-alerts", "data-manager-quotes-v3", "data-manager-quotes-v3-holdings", "data-manager-scanner", "data-manager-scanner-config", "data-manager-watchlists-holdings", "data-managers-examples", "data-managers-quotes-v3-fields", "icons-v<PERSON><PERSON><PERSON>", "india", "legacy-fission", "money", "news-user-settings", "newsdesk-tools", "pro", "pro-e2e", "proto", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-news-manager", "react-utils-data-hooks-quotes-manager", "react-utils-data-hooks-quotes-v3-manager", "react-utils-data-hooks-scanner-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-pro-tools", "react-utils-user-context", "react-utils-widget-tools", "ui-ads", "ui-ads-utils", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calculators", "ui-calendars", "ui-charts", "ui-comments", "ui-core", "ui-crypto", "ui-edge", "ui-entity", "ui-filter", "ui-forms", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-search", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-user", "ui-watchlist-ui", "ui-widgets", "utils-analytics", "utils-blocks", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-charting-libarary", "visualization-trading-view-light-weight-chart", "visualization-visualization-utils", "widget-chartwidget", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-option-chain", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder", "widgets-pro-calendar", "widgets-pro-insiders", "wnstn-chat"], "type": "bug", "updatedAt": "2025-04-11T12:54:28.760Z"}